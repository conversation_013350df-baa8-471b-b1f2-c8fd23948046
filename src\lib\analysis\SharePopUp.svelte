<script>
    import { self } from 'svelte/legacy';

	import { H2, P2 } from "$lib/ui";
	import ShareLink from "./ShareLink.svelte";

    /** @type {{changePopUp: any}} */
    let { changePopUp } = $props();
</script>
<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div class="overlay" onclick={self(changePopUp)}>
    <div class="pop-up">
        <button class="close-pop-up" onclick={changePopUp}>
            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="15" cy="15" r="15" fill="#66E2FF"/>
                <path d="M16.4 15L22.7 8.7C23.1 8.3 23.1 7.7 22.7 7.3C22.3 6.9 21.7 6.9 21.3 7.3L15 13.6L8.7 7.3C8.3 6.9 7.7 6.9 7.3 7.3C6.9 7.7 6.9 8.3 7.3 8.7L13.6 15L7.3 21.3C7.1 21.5 7 21.7 7 22C7 22.6 7.4 23 8 23C8.3 23 8.5 22.9 8.7 22.7L15 16.4L21.3 22.7C21.5 22.9 21.7 23 22 23C22.3 23 22.5 22.9 22.7 22.7C23.1 22.3 23.1 21.7 22.7 21.3L16.4 15Z" fill="white"/>
            </svg>
        </button>
    
        <H2>Share your Analysis</H2>
        <P2>Stay committed to your study plan with friends by sharing your test results and analytics.</P2>
        <ShareLink />
    </div>
</div>

<style>
    .overlay {
        background-color: rgba(0, 0, 0, 0.3);
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
    }

    .pop-up {
        background-color: white;
        padding: 4.6875rem 3.125rem;
        border: 1px solid var(--pitch-black);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        border-radius: 0.5rem;
        text-align: center;
        max-width: 40rem;

        display: flex;
        flex-direction: column;
        gap: 2rem;

        position: relative;
    }

    .close-pop-up {
        position: absolute;
        top: 1.25rem;
        right: 1.5rem;
    }
</style>