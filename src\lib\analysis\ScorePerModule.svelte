<script lang=ts>
	import { P2 } from "$lib/ui";
	import CircularProgressBar from "./CircularProgressBar.svelte";

    interface Props {
        scores: number[];
    }

    let { scores }: Props = $props();
</script>

<div class="container">
    <div class="section section--verbal">
        <P2 isBold={true}>Reading & Writing</P2>
        <CircularProgressBar size={130} score={scores[0]} total={27} text={"Module 1"} />
        <CircularProgressBar size={130} score={scores[1]} total={27} text={"Module 2"} />
    </div>
    <div class="section section--math">
        <P2 isBold={true}>Math</P2>
        <CircularProgressBar size={130} score={scores[2]} total={22} text={"Module 1"} />
        <CircularProgressBar size={130} score={scores[3]} total={22} text={"Module 2"} />
    </div>
</div>

<style>
    .container {
        display: inline-flex;
        padding: 1.5rem;
        gap: 2.625rem;
        flex: 1 0 auto;
        justify-content: center;
        
        background-color: var(--light-tangerine);
        border: 1px solid var(--pitch-black);
        border-radius: 0.5rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .section {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        justify-content: center;
        align-items: center;
    }

    .section--verbal {
        --primary-color: var(--sky-blue);
        --secondary-color: var(--light-sky-blue);
    }

    .section--math {
        --primary-color: var(--rose);
        --secondary-color: var(--light-rose);
    }

    @media (max-width: 540px) {
        .container {
            width: 100%;
            flex-direction: column;
            gap: 1.5rem;
            padding: 1rem;
        }
    }
</style>