
<!-- 
    @component
    A wrapper for sections in the landing pages.

    
    ## CSS Variables:
    - --bg-color: background color of the section
    - --height: height of the section
    - --padding-top: padding top of the section
    - --padding-bottom: padding bottom of the section
    - --overflow: overflow of the section

    Usage:
    ```tsx
    <SectionWrapper --height="100vh" --bg-color="var(--sky-blue)">
        <p>Content goes here</p>
    </SectionWrapper>
    ```
-->
<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<section>
    <div class="content-wrapper">
        {@render children?.()}
    </div>
</section>

<style>
    section {
        padding: var(--padding-top, 0) 4rem var(--padding-bottom, 0) 4rem;
        background: var(--bg-color);
        width: 100%;
        height: var(--height, auto);
        border-bottom: 0.25rem solid var(--pitch-black);

        display: flex;
        justify-content: center;
        align-items: center;

        overflow: var(--overflow, auto);
    }

    .content-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        max-width: 1440px;
    }

    @media (max-width: 768px) {
        section {
            padding: var(--padding-top, 0) 1rem var(--padding-bottom, 0) 1rem;
        }
    }
</style>