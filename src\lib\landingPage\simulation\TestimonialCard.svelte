<script>
	import { H2, H3 } from "$lib/ui";
	import P2 from "$lib/ui/typography/P2.svelte";

    /** @type {{name: any, profilePicUrl: any, oldScore: any, newScore: any, hasDescription?: boolean, review?: string, imageUrl?: string, description?: import('svelte').Snippet}} */
    let {
        name,
        profilePicUrl,
        oldScore,
        newScore,
        hasDescription = false,
        review = "",
        imageUrl = "",
        description
    } = $props();
</script>

<div class="card">
    <div class="avatar-and-name">
        <enhanced:img class="avatar" src={profilePicUrl} alt="avatar of student" loading="lazy" />
        <div class="name-container" style="justify-content: {hasDescription ? "flex-start" : "center"};">
            <div class="name">
                <H3>{name}</H3>
            </div>
            {@render description?.()}
        </div>
    </div>
    <H2><span class="old-score">{oldScore} </span><span class="new-score">➔ {newScore}</span></H2>
    {#if review}
        <P2>{review}</P2>
    {/if}
    <!-- TODO: Add lazyloading -->
    {#if imageUrl}
    <enhanced:img 
        class="review-image"
        loading="lazy"
        src={imageUrl} 
        alt="Score and review of student"
        sizes="(min-width: 540px) 720px, (min-width: 320px) 420px"
    />
    {/if}
</div>

<style>
    .card {
        background-color: var(--light-sky-blue);
        box-shadow: 0.25rem 0.25rem 0 var(--sky-blue);
        border: 0.1875rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        gap: 2rem;
        width: 100%;
        max-width: 40rem;
    }

    .avatar-and-name {
        display: inline-flex;
        gap: 1rem;
    }

    .avatar {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        border: 0.1875rem solid var(--sky-blue);
    }

    .name-container {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .name {
        width: fit-content;
    }

    .old-score {
        color: var(--tangerine);
    }

    .new-score {
        color: var(--aquamarine);
    }
    
    .review-image {
        object-fit: contain;
        width: 100%;
        height: var(--size);
    }

    @media (max-width: 540px) {
        .card {
            padding: 1.25rem;
        }
    }
</style>