<script>
	import { H5, P2 } from "$lib/ui";
	import ScoreChange from "./ScoreChange.svelte";
    import { browser } from "$app/environment";

    /** @type {{student: any, isSharing?: boolean}} */
    let { student, isSharing = false } = $props();
    let totalScoreChange = $state(0);
    let verbalScoreChange = $state(0);
    let mathScoreChange = $state(0);

    if (browser) {
        const previousVerbal = localStorage.getItem("previousVerbalScore");
        const previousMath = localStorage.getItem("previousMathScore");
        const previousTotal = parseInt(previousMath) + parseInt(previousVerbal);

        totalScoreChange = student.predictedTotalScore - previousTotal;
        verbalScoreChange = student.predictedVerbalScore - previousVerbal;
        mathScoreChange = student.predictedMathScore - previousMath;
    }

    let innerWidth = $state(0);
</script>

<svelte:window bind:innerWidth />

<div class="container">
    <div class="estimated-score">
        <div class="upper">
            <H5>Your Estimated Score</H5>
            <p class="score" style="--score={student.predictedTotalScore}">{student.predictedTotalScore}</p>
        </div>
        {#if !isSharing}
        <div class="lower">
            <ScoreChange scoreChange={totalScoreChange}/>
            <p class="score-change-text">Since last simulation</p>
        </div>
        {/if}
    </div>
    <div class="score-per-section">
        <div class="section">
            <span class="section-score section-score--verbal">{student.predictedVerbalScore}</span>
            <div class="section-right">
                <P2 isBold={true}>{innerWidth < 540 ? "Verbal" : "Reading & Writing"}</P2>
                {#if !isSharing}
                <ScoreChange scoreChange={verbalScoreChange}/>
                {/if}
            </div>
        </div>
        <div class="section">
            <span class="section-score section-score--math">{student.predictedMathScore}</span>
            <div class="section-right">
                <P2 isBold={true}>Math</P2>
                {#if !isSharing}
                <ScoreChange scoreChange={mathScoreChange}/>
                {/if}
            </div>
        </div>
    </div>
</div>

<style>
    .container {
        display: flex;
        flex-direction: column;
        padding: 1.5rem;
        gap: 1.75rem;
        
        background-color: white;
        border: 1px solid var(--pitch-black);
        border-radius: 0.5rem;
        flex: 1 0 auto;
        background-color: var(--light-yellow);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .estimated-score {
        display: flex;
        flex-direction: column;
        gap: 0.375rem;
        width: fit-content;
        flex-grow: 1;
    }

    .score {
        width: fit-content;
        font-size: 5.625rem;
        font-family: "Inter";
        font-weight: 800;
        -webkit-text-stroke: var(--pitch-black) 2px;
        paint-order: stroke fill;

        background: var(--sky-blue);
        background: linear-gradient(90deg, var(--sky-blue) 0%, var(--rose) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        -webkit-filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
        filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
    }

    .lower {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
    }

    .score-change-text {
        font-size: 0.875rem;
        font-family: "Inter";
        font-weight: 450;
        color: var(--pitch-black);
    }

    .score-per-section {
        display: flex;
        flex-direction: column;
        gap: 0.0625rem;
    }

    .section {
        display: inline-flex;
        gap: 0.625rem
    }

    .section-score {
        font-size: 2.8125rem;
        font-family: "Inter";
        font-weight: 800;
        font-variant-numeric: tabular-nums;
    }

    .section-score--verbal {
        color: var(--sky-blue);
    }

    .section-score--math {
        color: var(--rose);
    }

    .section-right {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    @media (max-width: 908px) {
        .container {
            align-items: center;
        }
    }

    @media (max-width: 540px) {
        .container {
            padding: 1rem;
        }
    }
</style>