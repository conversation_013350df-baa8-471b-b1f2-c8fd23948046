<script>
	import EstimatedScore from "./EstimatedScore.svelte";
	import ScorePerModule from "./ScorePerModule.svelte";
	import SendToMail from "./SendToMail.svelte";
	import SkillSummary from "./SkillSummary.svelte";

    /** @type {{student: any, skills: any, isSharing?: boolean}} */
    let { student, skills, isSharing = false } = $props();

    let overview = $state();
</script>
<div class="container" bind:this={overview}>
    <div class="score-overview">
        <EstimatedScore {student} {isSharing}/>
        <ScorePerModule scores={student.rawModuleScores}/>
    </div>  
    <SkillSummary {skills}/>
    <!-- <div class="mail-wrapper">
        <SendToMail bind:overview/>
    </div> -->
</div>

<style>
    .container {
        display: flex;
        flex-direction: column;
        padding: 2rem;
        gap: 1.25rem;

        box-shadow: 4px 4px 0 var(--pitch-black);
        width: 100%;
        max-width: 54rem;
        border: 1px solid var(--pitch-black);
        border-radius: 0.75rem;
        background-color: var(--light-aquamarine);
    }

    .score-overview {
        display: inline-flex;
        gap: 1.25rem;
        flex-wrap: wrap;
    }

    .mail-wrapper {
        display: flex;
        justify-content: center;
    }

    @media (max-width: 540px) {
        .container {
            padding: 0;
            border: none;
            background: none;
            box-shadow: none;
        }
    }
</style>