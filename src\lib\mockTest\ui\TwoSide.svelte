<script>
    
    /** @type {{isWalkthrough?: boolean, isMath: any, isSPR: any, children?: import('svelte').Snippet}} */
    let {
        isWalkthrough = false,
        isMath,
        isSPR,
        children
    } = $props();
</script>

<div class="wrapper">
    <div class:middle--small-gap={isSPR || isWalkthrough} class:center-wrapper={isMath && !isSPR && !isWalkthrough} class:middle={isSPR || !isMath || isWalkthrough}>
        {@render children?.()}
        {#if isSPR || !isMath || isWalkthrough}
            <div class="vr"></div>
        {/if}
    </div>
</div>


<style>
    .wrapper {
        width: 100%;
        height: calc(100svh - 180px);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .center-wrapper {
        display: flex;
        justify-content: center;
        height: calc(100vh - 180px);
        width: 100%;

        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .middle {
        height: calc(100vh - 180px);
        position: relative;
        width: 100%;
        max-width: 1600px;
        display: inline-grid;
        grid-template-columns: 1fr 1fr;
        justify-content: space-between;
        gap: 64px;
        /* margin: 50px 0; */
    }

    .middle--small-gap {
        gap: 16px;
    }

    .vr {
        width: 5px;
        height: calc(100% - 16px);
        background: #D9D9D9;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>