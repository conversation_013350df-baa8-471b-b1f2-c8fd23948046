<script lang="ts">
	import { Button, H3, P1, P2 } from "$lib/ui";

    interface Props {
        imageUrl: string;
        title: string;
        description: string;
        buttonLink?: string;
        isImageLeft: boolean;
    }
  
    let {
        imageUrl,
        title,
        description,
        buttonLink = null,
        isImageLeft,
    }: Props = $props();
  </script>
  
<div class="simulation-card" class:simulation-card--reversed={!isImageLeft}>
    <div class={{'image-container': true, 'left': isImageLeft, 'right': !isImageLeft}}>
        <div class="image-wrapper">
            <enhanced:img
                loading="lazy"
                src="/static/star.png"
                alt="DSAT simulation preview"
                class="preview-image"
            />
        </div>
    </div>
    <div class={{'content-container': true, 'left': !isImageLeft, 'right': isImageLeft}}>
        <H3>{title}</H3>
        <P1>{description}</P1>
        {#if buttonLink}
            <a href={buttonLink}><Button>Get Started</Button></a>
        {:else}
            <div class="coming-soon"><P2 isBold={true} --text-color="var(--purple)">COMING SOON</P2></div>
        {/if}
    </div>
</div>
  
<style>
    .simulation-card {
        border-radius: 1rem;
        box-shadow: 0.25rem 0.25rem 0 0 var(--sky-blue, #66e2ff);
        display: inline-flex;
        width: 100%;
        max-width: 50rem;
    }
    
    .simulation-card--reversed {
        flex-direction: row-reverse;
    }
  
    .image-container {
        border: 0.125rem solid #000;
        background: var(--sky-blue, #66e2ff);
        width: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .image-wrapper {
        width: 50%;
    }
  
    .preview-image {
        object-fit: contain;
        width: 100%;
        height: auto;
    }
  
    .content-container {
        border: 0.125rem solid #000;
        background: var(--light-purple, #eee5ff);
        display: flex;
        flex-direction: column;
        width: 50%;
        padding: 2rem;
        gap: 2rem;
    }

    .left {
        border-radius: 1rem 0 0 1rem;
    }

    .right {
        border-radius: 0 1rem 1rem 0;
    }

    .coming-soon {
        padding: 0.5rem 1rem;
        background-color: var(--light-aquamarine);
        width: fit-content;
		padding: 0.5rem 1rem;
		border-radius: 0.25rem;
		border: 1px solid var(--pitch-black);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    @media (max-width: 768px) {
        .simulation-card {
            flex-direction: column;
            max-width: 25rem;
        }

        .image-container {
            width: 100%;
            height: 18rem;
            border-radius: 1rem 1rem 0 0;
        }

        .content-container {
            width: 100%;
            border-radius: 0 0 1rem 1rem;
        }
    }
</style>