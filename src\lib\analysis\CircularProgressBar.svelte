<!-- 
  @component
  ## CircularProgressBar
  A component that displays a circular progress bar with a text label in the center and a fraction label at the bottom right corner.
    
  ## Props
    - `score` (number): The score to be displayed.
    - `total` (number): The total score.
    - `isVerbal` (boolean): A flag to determine the color of the progress bar.
    - `text` (string): The text to be displayed in the center of the progress bar.
    - `size` (number): The size of the progress bar.
 
  ## Usage
  ```html
  <CircularProgressBar {score} {total} isVerbal={false} text="Module 1" />
  ```
-->

<script lang="ts">
  interface Props {
    score: number;
    total: number;
    text: string;
    size?: number;
    isTopic?: boolean;
  }

  let {
    score,
    total,
    text,
    size = 130,
    isTopic = false
  }: Props = $props();

    let percentage = $derived(score / total);

    const stroke = 13;
    const center = size / 2;
    const radius = (size - stroke) / 2;
    const circumference = 2 * Math.PI * radius;

    let shortText = $derived(text.replace("Science", "Sci."))
</script>

<div class="circle-wrapper">
    <div class="box-shadow-wrapper">
      <svg class="svg" width={size} height={size} viewBox="0 0 {size + 4} {size + 4}" style="--center: {center}px">
          <circle class="bg"
            cx={center} cy={center} r={radius} fill="none" stroke="var(--primary-color)" stroke-width={stroke}
          />
          <circle class="border"
            cx={center} cy={center} r={radius} fill="none" stroke="var(--pitch-black)" stroke-width={stroke}
            stroke-dasharray={ (circumference * (1 - percentage) + 4) + " " + (circumference * percentage - 4) }
          />
          <circle class="fg"
            cx={center} cy={center} r={radius} fill="none" stroke="var(--secondary-color)" stroke-width={stroke + 0.3}
            stroke-dasharray={ circumference * (1 - percentage) + " " + circumference * percentage }
          />
      </svg>
      <div class="fraction">
          {#if isTopic}
            <span>{Math.round(score * 100 / total)}%</span>
          {:else}
            <span>{score}/{total}</span>
          {/if}
      </div>
    </div>
    

    <div class="middle-text">
        {shortText}
    </div>
</div>


<style>
    circle.fg {
        rotate: -90deg;
        transform-origin: var(--center) var(--center);
    }

    circle.border {
        rotate: -91.7deg;
        transform-origin: var(--center) var(--center);
    }

    .circle-wrapper {
        position: relative;
        display: flex;
        width: fit-content;
        position: relative;
    }

    /* Temporary solution to create a border */
    .svg {
        filter: drop-shadow(-1px 0 var(--pitch-black)) drop-shadow(0 -1px var(--pitch-black)) drop-shadow(0 1px var(--pitch-black)) drop-shadow(1px 0 var(--pitch-black));
    }

    .box-shadow-wrapper {
        filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
    }

    .middle-text {
        font-family: "Inter";
        font-size: 1.125rem;
        font-weight: 600;

        position: absolute;
        top: 50%;
        left: 0;
        transform: translate(0, -50%);
        width: 100%;
        text-align: center;
    }

    .fraction {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0.25rem;
        border: 1px solid var(--pitch-black);
        border-radius: 0.25rem;

        position: absolute;
        right: 2px;
        bottom: 18px;
        min-width: 47px;
        min-height: 25px;
        background-color: white;

        font-family: "Inter";
        font-size: 0.875rem;
        font-weight: 450;
    }
</style>
