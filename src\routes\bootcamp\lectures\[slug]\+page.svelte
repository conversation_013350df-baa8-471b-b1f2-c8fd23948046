<script>
	import { <PERSON><PERSON>, H2 } from "$lib/ui";

    /** @type {{data: any}} */
    let { data } = $props();
</script>

<svelte:head>
    <title>{data.title} - DSAT16</title>
</svelte:head>

<div class="lecture-wrapper">
    <div class="lecture">
        <H2>
            {data.title}
        </H2>
        <div class="lecture-video">{@html data.video}</div>
        <div class="lecture-buttons">
            <a href="/bootcamp/notes/{data.slug}"><Button>Notes</Button></a> 
            {#if data.hasAR}
                <a href="/bootcamp/additional-resources/{data.slug}"><Button>Additional Resources</Button></a>
            {/if}
        </div>
    </div>
</div>

<style>
    a {
        text-decoration: none;
    }

    .lecture-wrapper {
        margin-left: 320px;
        padding: 50px 32px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    


    .lecture-video {
        background: var(--light-sky-blue, #DAF8FF);
        aspect-ratio: 16 / 9;
        width: calc(90vw - 320px);
        max-width: 925px;
        margin: 40px 0;
        border: 3px solid #000;
        box-shadow: 4px 4px 0px 0px #000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .lecture-buttons {
        display: inline-flex;
        gap: 24px;
        flex-wrap: wrap;
    }


    @media (max-width: 1023px) {
        .lecture-wrapper {
            margin-left: 0;
            padding-top: 100px;
        }

        .lecture-video {
            width: 85vw;
            margin: 40px auto;
        }
    }
</style>