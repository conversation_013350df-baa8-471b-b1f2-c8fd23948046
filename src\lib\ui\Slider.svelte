<script>
    /** @type {{label: any, value: any, min: any, max: any, step: any, required?: boolean, disabled?: boolean, isRightAligned?: boolean}} */
    let {
        label,
        value = $bindable(),
        min,
        max,
        step,
        required = false,
        disabled = false,
        isRightAligned = false
    } = $props();

    function clamp() {
        value = Math.min(Math.max(value, min), max);
    } 

    function handleKeyDown(event) {
        if (event.key === "Enter") {
            clamp();
        }
    }
</script>
<div class="wrapper">
    <p class="label" class:right-align={isRightAligned}>{label}</p>
    <div class="slider-container">
        <input class="slider" type="range" {min} {max} {step} {required} bind:value={value} {disabled} style="--width: {(value - min) * 100 / (max - min)}%">
        <input class="input-box" type="number" {step} {required} {disabled} bind:value={value} onblur={clamp} onkeydown={handleKeyDown}>
    </div>
</div>
    
<style>
    input:disabled {
        cursor: default;
    }

    .wrapper {
        display: inline-flex;
        gap: 2.03125rem;
    }

    .label {
        flex: 1 0 auto;
        display: flex;
        align-items: center;
        justify-content: start;
    }

    .right-align {
        justify-content: end;
    }

    .slider-container {
        display: inline-flex;
        width: 100%;
        gap: 1.40625rem;
        justify-content: center;
        align-items: center;
        flex: 0 1 auto;
        max-width: 620px;
    }

    .slider {
        -webkit-appearance: none;
        appearance: none;
        width: 100%;
        height: 0.625rem;
        background: var(--light-sky-blue); 
        border-radius: 20px;
        outline: none;
        position: relative;
        z-index: 0;
    }

    .slider::before {
        content: "";
        position: absolute;
        width: var(--width);
        height: 0.625rem;
        background: var(--sky-blue);
        border-radius: 20px;
        z-index: -1;
    }

    .slider:disabled, .slider:disabled::before {
        background: #D9D9D9;
    }

    .input-box {
        padding: 10px;
        border: 1px solid #A7A7A7;
        border-radius: 8px;
        font-family: "Inter";
        font-size: 1.125rem;
        text-align: center;
        width: 65px;
    }

    .input-box:disabled {
        text-decoration-color: #777777;
    }

    .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        background: var(--white);
        border: 0.375rem solid var(--sky-blue);
        cursor: pointer;
    }

    .slider::-moz-range-thumb {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        background: var(--white);
        border: 0.375rem solid var(--sky-blue);
        cursor: pointer;
    }

    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }
</style>