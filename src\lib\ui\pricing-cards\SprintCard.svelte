<script>
	import PricingCard from "./PricingCard.svelte";    

    const testSprint = [
        {
            text: "1 DSAT16 Simulation",
            subtext: "(98 Questions, 2 hours 24 minutes)"
        },
        {
            text: "Full answer + Explanation",
            subtext: null
        },
        {
            text: "Comprehensive Analysis",
            subtext: null
        },
        {
            text: "Actionable Study Plan",
            subtext: null
        },
    ]
</script>

<PricingCard 
    label="SAT Sprint"
    description="For those whose test day is coming close. Perfect for SAT prep sprint."
    price="€12.16"
    checklist={testSprint}
    priceId="price_1QUtLTJPIuRILQq6yeFq5vVE"
    --label-bg-color=var(--light-sky-blue)
    --icon-color=var(--aquamarine)
    --button-bg-color=var(--sky-blue)
/>