<script>
    /** @type {{data: any, i: any, studentAnswers: any, studentCross: any, setAnswer: any, setCross: any, isEliminateTool: any}} */
    let {
        data,
        i,
        studentAnswers,
        studentCross,
        setAnswer,
        setCross,
        isEliminateTool
    } = $props();
    const letters = ['A', 'B', 'C', 'D'];
</script>

<div class="choices">
    {#each data.questions[i].choices as choice, index}
        <div class="answer-wrapper">
            <button class="answer-container" class:answer-border={studentAnswers[i] === index} class:crossed={studentCross[i][index]} onclick={() => setAnswer(index)}>
                <div class="answer-letter-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 27 27">
                        <circle cx="13.5" cy="13.5" r="12.5" fill={studentAnswers[i] === index ? '#66E2FF' : "white"} stroke="#333333"/>
                    </svg>
                    <span class="answer-letter nonselect">{letters[index]}</span>
                </div>

                {#if Array.isArray(choice)}
                    <div class="choice-table-wrapper">
                        <table class="table table--choice">
                            <tbody>
                                {#each choice as row }
                                <tr>
                                    {#each row as item }
                                        <td>{@html item}</td>
                                    {/each}
                                </tr>
                            {/each}
                            </tbody>
                        </table>
                    </div>
                {:else}
                    <p class="answer-text">{@html choice}</p>
                {/if}
            </button>

            {#if isEliminateTool}
                {#if studentCross[i][index]}
                    <button class='undo-cross nonselect' onclick={() => setCross(index)}><u>Undo</u></button>
                {:else}
                    <button class="cross-choice nonselect" onclick={() => {setCross(index)}}>{letters[index]}</button>
                {/if}
            {/if}
            
        </div>
    {/each}
</div>

<style>
    .table {
        width: 100%;
        text-align: center;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table tr {
        width: 50%;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table td {
        padding: 14px 10px;
        border: 1px solid black;
        border-collapse: collapse;
        color: var(--Charcoal, #333);

        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    button {
        border: 0;
        background: none;
    }

    .answer-wrapper {
        display: flex;
        align-items: center;
        gap: 20px;
        width: 98.4%;
    }

    .cross-choice {
        width: 21px;
        height: 21px;
        flex-shrink: 0;
        fill: #FFF;
        stroke-width: 1px;
        stroke: #000;
        border-radius: 21px;
        border: black solid 1.5px;
        color: #000;
        font-family: 'Inter';
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin: 0 9px;
    }

    .cross-choice::after {
        content: '';
        position: absolute;
        width: 150%;
        background-color: black;
        height: 2px;
    }

    .crossed {
        position: relative;
        display: flex;
        pointer-events: none;
        opacity: 0.6;
    }

    .crossed::after {
        content: '';
        position: absolute;
        width: 102%;
        background-color: black;
        height: 2px;
        left: -1%;
    }

    .undo-cross {
        color: #000;
        font-family: 'Inter';
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: 25.1px; /* 167.333% */
        text-decoration-line: underline;
    }


    .answer-container {
        display: inline-flex;
        min-height: 50px;
        width: 100%;
        padding: 12px 13px;
        align-items: center;
        gap: 21px;
        border-radius: 8px;
        border: 1px solid var(--charcoal, #333);
    }

    .answer-container:hover {
        opacity: initial;
    }

    .answer-border {
        outline: 3px solid #66E2FF;
    }

    .answer-letter-container {
        display: flex;
        align-items: center;
        position: relative;
        stroke-width: 2px;
        stroke: var(--charcoal, #333);
    }

    .answer-letter {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        color: var(--charcoal, #333);
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .answer-text {
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
        text-align: start;
        user-select: text;
    }

    .choice-table-wrapper {
        margin: 12px 0;
    }

    .table--choice td {
        padding: 13px 22px;
    }

    .choices {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        width: 100%;
    }

    .choices:last-child {
        padding-bottom: 16px;
    }
</style>