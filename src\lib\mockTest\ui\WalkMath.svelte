<script lang="ts">
	import { onMount } from "svelte";
	import Graph from "./Graph.svelte";
	import Intro from "./Intro.svelte";
	import MarkBar from "./MarkBar.svelte";
	import MidLeft from "./MidLeft.svelte";
	import Question from "./Question.svelte";
	import WalkAnswerBox from "./WalkAnswerBox.svelte";
	import WalkChoices from "./WalkChoices.svelte";
	import WalkSolution from "./WalkSolution.svelte";


    let {
        i,
        student,
        test,
        m = -1,
        isMath,
        isSPR
    } = $props();

    const isWalkthrough = true;

    const PRIOR = [0, 27, 54, 76];
    let numberOfPriorQuestions = $derived(PRIOR[m] ?? 0);

    // Use mathjax because there is a problem with katex. Will fix this later.
    $effect(() => {
        i;
        try {
            if (isMath) MathJax.typeset();
        } catch (error) {
            
        }
    })

    onMount(() => {
        // Mathjax
        window.MathJax = {
                tex: {inlineMath: [['$', '$'], ['\\(', '\\)']]},
                svg: {fontCache: 'global'}
        };

        let script = document.createElement('script');
        script.src = "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js";
        document.head.append(script);
    })
</script>

<MidLeft {isSPR} {isWalkthrough}>
    <!-- Index, Mark, and Elimination Bar -->
    <MarkBar {i} isSPR={false} {isWalkthrough}/>

    {#if test.questions[i].intro}
        {#key i}
            <Intro intro={test.questions[i].intro}/>
        {/key}
    {/if}

    {#if test.questions[i].graph}
        <Graph isMath={true} graph={test.questions[i].graph}/>
    {/if}

    {#key i}
        <Question question={test.questions[i].question} isMath={true} {isWalkthrough}/>
    {/key}

    {#if isSPR}
        <WalkAnswerBox value={student.answers[i + numberOfPriorQuestions]} correctAnswer={test.questions[i].correctAnswer}/>
    {:else}
        {#key i}
            <WalkChoices {i} {test} {student} {m}/>
        {/key}
    {/if}
</MidLeft>

<WalkSolution explanation={test.questions[i].explanation} {isMath}/>

