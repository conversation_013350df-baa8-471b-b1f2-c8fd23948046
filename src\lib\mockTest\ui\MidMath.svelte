<script lang="ts">
	import AnswerBox from "$lib/mockTest/ui/AnswerBox.svelte";
	import { onMount } from "svelte";
	import Choices from "./Choices.svelte";
    import Graph from "./Graph.svelte";
    import Intro from "./Intro.svelte";
	import MarkBar from "./MarkBar.svelte";
	import Question from "./Question.svelte";

    const isMath = true;

    
    let {
        isCalculatorOpen,
        data,
        i,
        isMarked,
        setMarked,
        isEliminateTool,
        toggleElimination,
        setAnswer,
        setCross,
        isSPR,
        studentCross,
        studentAnswers
    } = $props();

    // Use mathjax because there is a problem with katex. Will fix this later.
    $effect(() => {
        i;
        try {
            if (isMath) MathJax.typeset();
        } catch (error) {
            
        }
    })

    onMount(() => {
        // Mathjax
        window.MathJax = {
                tex: {inlineMath: [['$', '$'], ['\\(', '\\)']]},
                svg: {fontCache: 'global'}
        };

        let script = document.createElement('script');
        script.src = "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js";
        document.head.append(script);
    })
</script>

<div class={!isSPR ? "center" : "right-mid-math"} class:right--calc={isCalculatorOpen && !isSPR}>

    <!-- Index, Mark, and Elimination Bar -->
    <MarkBar {i} {isMarked} {setMarked} {isEliminateTool} {toggleElimination} {isSPR}/>
    
    {#key i}
        {#if data.questions[i].intro}
            <Intro intro={data.questions[i].intro}/>
        {/if}
    {/key}
    
    {#if data.questions[i].graph}
        <Graph graph={data.questions[i].graph} {isMath}/>
    {/if}

    <!-- Math Question -->
    {#key i}
        <Question question={data.questions[i].question} {isMath}/>
    {/key}

    <!-- Math Answer -->
    {#key i}
        {#if !isSPR}
            <Choices {data} {i} {isEliminateTool} {studentAnswers} {studentCross} {setAnswer} {setCross} />
        {:else}
            <AnswerBox {i} {studentAnswers}/>
        {/if}
    {/key}
</div>


<style>
    .center {
        width: 100%;
        max-width: 800px;
        margin: 50px 32px 0 32px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        transition: margin 0.2s;
    }

    .right-mid-math {
        display: flex;
        /* justify-self: end; */
        flex-direction: column;
        margin: 32px 64px 0 35px;
        max-width: 651px;
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
        gap: 6px;
    }
    
    .right--calc {
        margin-left: 500px;
    }

    :global(.right-mid-math > *) {
        width: calc(100% - 6px);
    }
</style>