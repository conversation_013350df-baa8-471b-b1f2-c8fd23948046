<script lang=ts>
	import { H2, H3, H4, H5, P2, P2<PERSON><PERSON><PERSON> } from '$lib/ui';
	import HorizontalScorePerModule from './HorizontalScorePerModule.svelte';
	import Overview from './Overview.svelte';
	import SkillAssessmentChart from './SkillAssessmentChart.svelte';
	import { browser } from '$app/environment';
	import StudyPlan from './StudyPlan.svelte';
	import CircularProgressBar from './CircularProgressBar.svelte';
	import ShareLink from './ShareLink.svelte';

	const testPercentile = {
		'1600': '99',
		'1590': '99',
		'1580': '99',
		'1570': '99',
		'1560': '99',
		'1550': '99',
		'1540': '99',
		'1530': '99',
		'1520': '98',
		'1510': '98',
		'1500': '98',
		'1490': '97',
		'1480': '97',
		'1470': '96',
		'1460': '96',
		'1450': '96',
		'1440': '95',
		'1430': '95',
		'1420': '94',
		'1410': '94',
		'1400': '93',
		'1390': '93',
		'1380': '92',
		'1370': '91',
		'1360': '91',
		'1350': '90',
		'1340': '89',
		'1330': '89',
		'1320': '88',
		'1310': '87',
		'1300': '86',
		'1290': '85',
		'1280': '85',
		'1270': '84',
		'1260': '83',
		'1250': '82',
		'1240': '81',
		'1230': '80',
		'1220': '79',
		'1210': '77',
		'1200': '76',
		'1190': '75',
		'1180': '74',
		'1170': '73',
		'1160': '71',
		'1150': '70',
		'1140': '69',
		'1130': '67',
		'1120': '66',
		'1110': '64',
		'1100': '63',
		'1090': '61',
		'1080': '60',
		'1070': '58',
		'1060': '57',
		'1050': '55',
		'1040': '54',
		'1030': '52',
		'1020': '51',
		'1010': '49',
		'1000': '47',
		'990': '46',
		'980': '44',
		'970': '43',
		'960': '41',
		'950': '40',
		'940': '38',
		'930': '37',
		'920': '35',
		'910': '33',
		'900': '32',
		'890': '30',
		'880': '29',
		'870': '27',
		'860': '26',
		'850': '24',
		'840': '22',
		'830': '21',
		'820': '19',
		'810': '18',
		'800': '16',
		'790': '15',
		'780': '13',
		'770': '12',
		'760': '11',
		'750': '9',
		'740': '8',
		'730': '7',
		'720': '6',
		'710': '5',
		'700': '4',
		'690': '3',
		'680': '3',
		'670': '2',
		'660': '2',
		'650': '1',
		'640': '1',
		'630': '1',
		'620': '1',
		'610': '1',
		'600': '1',
		'590': '1',
		'580': '1',
		'570': '1',
		'560': '1',
		'550': '1',
		'540': '1',
		'530': '1',
		'520': '1',
		'510': '1',
		'500': '1',
		'490': '1',
		'480': '1',
		'470': '1',
		'460': '1',
		'450': '1',
		'440': '1',
		'430': '1',
		'420': '1',
		'410': '1',
		'400': '1'
	};

	const verbalPercentile = {
		'800': '99%+',
		'790': '99%+',
		'780': '99%+',
		'770': '99%+',
		'760': '99%+',
		'750': '98%',
		'740': '97%',
		'730': '96%',
		'720': '95%',
		'710': '94%',
		'700': '93%',
		'690': '92%',
		'680': '91%',
		'670': '89%',
		'660': '87%',
		'650': '85%',
		'640': '83%',
		'630': '81%',
		'620': '79%',
		'610': '77%',
		'600': '74%',
		'590': '72%',
		'580': '69%',
		'570': '66%',
		'560': '63%',
		'550': '60%',
		'540': '58%',
		'530': '55%',
		'520': '51%',
		'510': '48%',
		'500': '45%',
		'490': '42%',
		'480': '38%',
		'470': '35%',
		'460': '32%',
		'450': '29%',
		'440': '26%',
		'430': '23%',
		'420': '20%',
		'410': '17%',
		'400': '14%',
		'390': '12%',
		'380': '10%',
		'370': '8%',
		'360': '6%',
		'350': '4%',
		'340': '3%',
		'330': '2%',
		'320': '2%',
		'310': '1%',
		'300': '1%',
		'290': '1%',
		'280': '1%',
		'270': '1%-',
		'260': '1%-',
		'250': '1%-',
		'240': '1%-',
		'230': '1%-',
		'220': '1%-',
		'210': '1%-',
		'200': '1%-'
	};

	const mathPercentile = {
		'800': '99%+',
		'790': '99%',
		'780': '98%',
		'770': '97%',
		'760': '96%',
		'750': '95%',
		'740': '95%',
		'730': '94%',
		'720': '93%',
		'710': '92%',
		'700': '92%',
		'690': '90%',
		'680': '89%',
		'670': '88%',
		'660': '87%',
		'650': '85%',
		'640': '84%',
		'630': '82%',
		'620': '81%',
		'610': '79%',
		'600': '77%',
		'590': '75%',
		'580': '72%',
		'570': '70%',
		'560': '67%',
		'550': '65%',
		'540': '62%',
		'530': '59%',
		'520': '55%',
		'510': '52%',
		'500': '49%',
		'490': '46%',
		'480': '44%',
		'470': '41%',
		'460': '38%',
		'450': '36%',
		'440': '33%',
		'430': '30%',
		'420': '27%',
		'410': '24%',
		'400': '21%',
		'390': '18%',
		'380': '15%',
		'370': '12%',
		'360': '10%',
		'350': '7%',
		'340': '5%',
		'330': '4%',
		'320': '3%',
		'310': '2%',
		'300': '1%',
		'290': '1%',
		'280': '1%',
		'270': '1%',
		'260': '1%',
		'250': '1%-',
		'240': '1%-',
		'230': '1%-',
		'220': '1%-',
		'210': '1%-',
		'200': '1%-'
	};

	const colorOfTopics = ["purple", "aquamarine", "yellow", "tangerine"];

	interface Props {
		studyPlan: any;
		weaknesses: { questionType: string, review: string }[];
		strengths: { questionType: string, review: string }[];
		student: {
		 simulation: string; predictedTotalScore: number; predictedVerbalScore: number; predictedMathScore: number; rawModuleScores: number[] 
};
		scoreOfTopics: { topic: string; score: number; count: number }[];
		scoreOfTypes: { questionType: string; score: number; count: number }[];
	}

	let {
		studyPlan,
		weaknesses,
		strengths,
		student,
		scoreOfTopics,
		scoreOfTypes
	}: Props = $props();
	let skills = $derived([
		{
			sectionTitle: 'Reading',
			isVerbal: true,
			sections: scoreOfTypes.slice(0, 4)
		},
		{
			sectionTitle: 'Writing',
			isVerbal: true,
			sections: scoreOfTypes.slice(4, 8)
		},
		{
			sectionTitle: 'Math',
			isVerbal: false,
			sections: scoreOfTypes.slice(8)
		}
	]);

	function clamp(value: number, min: number, max: number): number {
		return Math.min(Math.max(value, min), max);
	}

	let aimScore: number | null = $state(null);
	
	if (browser) {
		aimScore = JSON.parse(localStorage.getItem('aimScore'));
	}

	// Map the aimScore to the lower bound of aimMath. the other bounds are calculated based on this value. 
	const aimModules = {
		1600: 800,
		1590: 790,
		1560: 780,
		1530: 770,
		1500: 750,
		1450: 750,
		1400: 730,
		1350: 700,
		1300: 680,
	};

	// Use a simpler formula for aimScore < 1300
	let aimMathLower = $derived(aimScore > 1300 
		? aimModules[Object.keys(aimModules).find(key => parseInt(key) >= aimScore)] 
		: clamp(Math.ceil(aimScore / 20) * 10 + 40, 200, 800));

	// Calculate the bounds based on the math lower bound
	let aimMathUpper = $derived(clamp(aimMathLower + 50, 200, 800));
	let aimVerbalLower = $derived(clamp(aimScore - aimMathUpper, 200, 800));
	let aimVerbalUpper = $derived(clamp(aimScore - aimMathLower, 200, 800));

	let aimMathRange = $derived(aimMathLower == aimMathUpper ? aimMathLower : `${aimMathLower}-${aimMathUpper}`)
	let aimVerbalRange = $derived(aimVerbalLower == aimVerbalUpper ? aimVerbalLower : `${aimVerbalLower}-${aimVerbalUpper}`)
	// Calculate the points needed to increase in Math and Verbal sections
	let needToIncreaseMath = $derived(aimMathLower - student.predictedMathScore + 30);
	let needToIncreaseVerbal = $derived(aimScore - aimMathLower - 30 - student.predictedVerbalScore);

	let sortedScoreOfTopics = $derived(scoreOfTopics.sort((a, b) => b.score / b.count - a.score / a.count));
	let needImprovementTopics = $derived(sortedScoreOfTopics.slice(2).map(topic => topic.topic));

	let studyPlanTime = $state(0);

	function setStudyPlanTime(time: number) {
		studyPlanTime = time;
	}
</script>

<div class="container">
	<h1 class="analysis-title">Your Performance Analysis is Here!</h1>
	<div class="analysis-section">
		<H2>1. Overview</H2>

		<Overview {student} {skills} />

		<P2Wrapper>
			<p>
				Your estimated SAT score is {student.predictedTotalScore}, which places you in the top {100 -
					testPercentile[student.predictedTotalScore]}% of all test-takers. By section, your Reading
				& Writing score is {student.predictedVerbalScore}, higher than {verbalPercentile[student.predictedVerbalScore]}
				of other test takers, and your Math score is {student.predictedMathScore}, higher than {mathPercentile[student.predictedMathScore]}
				of other test takers.
			</p>

			<!-- 
			Component section that displays different messages based on the student's aim score and predicted performance:
			- If no aim score is set: Shows encouragement to read the report and study plan
			- If predicted score meets/exceeds aim score: Shows congratulatory message
			- If predicted score is below aim score: Shows specific target scores needed for Math and Verbal sections
			-->
			<!--  -->
			{#if !aimScore}
				<p>
					If you want to improve your score as much as possible, continue reading the report, especially the study plan, 
					so you know what you need to focus on and what action you can take.
				</p>
			{:else if student.predictedTotalScore >= aimScore}
				<p>Congratulations! Your current result already matched your aim! Try to maintain your performance by continuously practicing.</p>
			{:else}
				<p>
					To make it to your target score of {aimScore} points, we recommend that you aim to 
					reach {aimMathRange} in
					Math and {aimVerbalRange} in Verbal.

					<!-- Display the suggestion based on the student's current score -->
					Therefore, you would need to further increase 
					{#if needToIncreaseMath > 0 && needToIncreaseVerbal <= 0}
						{Math.min(needToIncreaseMath, aimScore - student.predictedTotalScore)} points in Math.
					{:else if needToIncreaseMath <= 0 && needToIncreaseVerbal > 0}
						{Math.min(needToIncreaseVerbal, aimScore - student.predictedTotalScore)} points in Verbal.
					{:else if needToIncreaseMath > 0 && needToIncreaseVerbal > 0}
						{needToIncreaseMath} points in Math and {needToIncreaseVerbal} points in Verbal.
					{/if}
				</p>
				
				<p>
					Continue reading the report, especially the study plan, so you know what you need to focus
					on and what action you can take.
				</p>
			{/if}
		</P2Wrapper>

		<hr class="hr"/>

		<div class="subsection">
			<H2>2. Module Analytics</H2>
			<P2Wrapper>
				<p>
					In a digital multistage adaptive SAT test, each test section (Reading and Writing, Math)
					is divided into two stages, called modules. You will answer a set of questions in the
					first module before moving on to the next. The questions in the second module are
					configured based on performance in the first module.
				</p>
				<p>
					If you perform well in Module 1, you will be given a harder version of Module 2 and get a
					chance to get 400-800 points. Conversely, if you made a certain number of mistakes in
					Module 1, your Module 2 will be the easier version, and therefore you can only get 200-600
					points.
				</p>
			</P2Wrapper>
		</div>

		<HorizontalScorePerModule --bg-color=var(--light-tangerine)>
			{#snippet circles()}
					
				{#each [student.rawModuleScores.slice(0, 2), student.rawModuleScores.slice(2)] as section, i}
					<div class="horizontal-section-wrapper">
						<div class="circle-wrapper">
							{#each section as score, j}
								<CircularProgressBar 
									{score} 
									total={i == 0 ? 27 : 22} 
									text="Module {j + 1}" 
									isTopic={false}
									--primary-color={i == 0 ? "var(--sky-blue)" : "var(--rose)"}	
									--secondary-color={i == 0 ? "var(--light-sky-blue)" : "var(--light-rose)"}	
								/>
							{/each}
						</div>
						<H5>{i == 0 ? "Reading & Writing" : "Math"}</H5>
					</div>
				{/each}
				
					{/snippet}
		</HorizontalScorePerModule>

		<P2Wrapper>
			<p>
				In the Reading & Writing section of this simulation, you got {student.rawModuleScores[0]}/27
				questions correct in Module 1, leading to {student.rawModuleScores[0] >= 20 ? "a harder" : "an easier"} 
				version for Module 2 in the official test. 
				
				{#if aimScore}
					As you aim to score {aimVerbalRange} in the Verbal section, try to {aimVerbalLower >= 670 
					? "aim for 100% accuracy rate in Module 1" 
					: aimVerbalLower >= 600 
					? "make no more than 3-4 mistakes in Module 1" 
					: "make no more than 6-7 mistakes in Module 1"}.
				{/if}
			</p>
			<p>
				In the Math section, you got {student.rawModuleScores[2]}/22 questions correct in Module 1, leading to 
				{student.rawModuleScores[2] >= 14 ? "a harder" : "an easier"} version for Module 2 in the official test.

				{#if aimScore}
					As you aim to score {aimMathRange} in the Math section, try to {aimMathLower >= 700 
					? "aim for 100% accuracy rate in Module 1" 
					: aimMathLower >= 600 
					? "make no more than 3-4 mistakes in Module 1" 
					: "make no more than 7-8 mistakes in Module 1"}.
				{/if}
			</p>
			{#if !aimScore}
			<p>
				As Module 1 is less challenging and significantly influences your overall result, 
				try not to make any silly mistakes and lose your precious points in these Modules.
			</p>
			{/if}
		</P2Wrapper>

		<hr class="hr"/>

		<div class="subsection">
			<H2>3. Writing Performance</H2>
			<P2>
				This part test your fluency in academic English language. It appears in the second half of
				the module. Although these Writing questions are considerably easier than Reading questions,
				they contain a lot of traps that you might fall into if your mind is fatigue after the first
				half and lose your concentration.
			</P2>
		</div>

		<SkillAssessmentChart studentPerformances={scoreOfTypes.slice(0, 4)} />

		<div class="tip-section">
			<H3>
				<div class="tip-header">
					<div class="tip-icon-wrapper">
						<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g clip-path="url(#clip0_3238_3241)">
							<path d="M21.1124 3.16983V3.21291C20.0284 2.4097 18.795 1.82735 17.4823 1.50115C16.0017 1.13322 14.4576 1.10044 12.9627 1.40521C10.9722 1.80733 9.14602 2.79163 7.7158 4.23325C6.28554 5.67492 5.31575 7.50896 4.92949 9.50267H4.92949L4.92926 9.50386C4.64725 10.9984 4.6985 12.5368 5.07934 14.0092C5.46019 15.4817 6.16126 16.852 7.13255 18.0224L7.13245 18.0225L7.13782 18.0285C7.97783 18.9764 8.45968 20.1883 8.49993 21.4542V25.2002C8.49993 26.2611 8.92136 27.2785 9.6715 28.0286C10.4216 28.7788 11.4391 29.2002 12.4999 29.2002H17.4999C18.5608 29.2002 19.5782 28.7788 20.3284 28.0286C21.0785 27.2785 21.4999 26.2611 21.4999 25.2002V21.6913C21.541 20.2857 22.0578 18.9355 22.9658 17.8616L22.9659 17.8617L22.9693 17.8574C24.6672 15.7571 25.4675 13.0717 25.1964 10.3847C24.9253 7.69763 23.6047 5.22617 21.5215 3.50739L21.1124 3.16983ZM22.2499 11.4501L22.2499 11.4516C22.2589 13.1052 21.6914 14.7102 20.645 15.9906C19.4049 17.4622 18.6608 19.2853 18.5152 21.2002H16.4999V17.7002C16.4999 17.3024 16.3419 16.9209 16.0606 16.6396C15.7793 16.3583 15.3978 16.2002 14.9999 16.2002C14.6021 16.2002 14.2206 16.3583 13.9393 16.6396C13.658 16.9209 13.4999 17.3024 13.4999 17.7002V21.2002H11.4921C11.4043 19.3159 10.6825 17.5137 9.44015 16.088C8.63833 15.1266 8.09911 13.9739 7.87503 12.7422C7.65078 11.5096 7.74977 10.2399 8.16234 9.05696C8.57491 7.87402 9.28698 6.81818 10.2291 5.99238C11.1712 5.1666 12.3113 4.59905 13.538 4.34505C14.5923 4.128 15.6817 4.14843 16.7271 4.40486C17.7725 4.6613 18.7478 5.14733 19.582 5.82765C20.4162 6.50798 21.0884 7.36553 21.5499 8.33804C22.0114 9.31056 22.2505 10.3736 22.2499 11.4501ZM18.4999 25.2002C18.4999 25.4654 18.3946 25.7198 18.207 25.9073C18.0195 26.0949 17.7651 26.2002 17.4999 26.2002H12.4999C12.2347 26.2002 11.9804 26.0949 11.7928 25.9073C11.6053 25.7198 11.4999 25.4654 11.4999 25.2002V24.2002H18.4999V25.2002Z" fill="#FFC800" stroke="black" stroke-width="0.5"/>
							</g>
							<defs>
							<clipPath id="clip0_3238_3241">
							<rect width="30" height="30" fill="white" transform="translate(0 0.200195)"/>
							</clipPath>
							</defs>
						</svg>	
					</div>
	
					<span>Tip to improve:</span>
				</div>
			</H3>
			<P2
				>You should spend time to work on the Writing questions first, when your mind is still
				clear. In addition, since these questions are easy, completing them in a short amount of
				time at the start of the test will give you a confidence boost, which surely helps improve
				your overall performance.</P2
			>
		</div>

		<hr class="hr"/>

		<div class="subsection">
			<H2>4. Reading Performance</H2>
			<P2>
				This part tests your ability in understanding academic English language, and it appears in
				the first half of a Verbal module. They are often more challenging and longer than Writing
				questions, therefore very taxing on the mind.
			</P2>
		</div>

		<SkillAssessmentChart 
			studentPerformances={scoreOfTypes.slice(4, 8)}
			--primary-color=var(--light-purple)
			--secondary-color=var(--light-tangerine)
		/>

		<P2>
			The passages will be in English academic writing in one of the four topics: Natural Science,
			Social Science, Fiction, and Humanities. Below is your performance based on topic:
		</P2>

		<HorizontalScorePerModule --bg-color=var(--light-sky-blue)>
			{#snippet circles()}
					
					{#each scoreOfTopics as { topic, score, count }, i}
						<CircularProgressBar 
							{score} 
							total={count} 
							text={topic} 
							size={150}
							isTopic={true}
							--primary-color="var(--{colorOfTopics[i]})"	
							--secondary-color="var(--light-{colorOfTopics[i]})"	
						/>
					{/each}
				
					{/snippet}

			{#snippet header()}
					
					<H5>Your Performance by Topic</H5>
				
					{/snippet}
		</HorizontalScorePerModule>

		<div class="tip-section">
			<H3>
				<div class="tip-header">
					<div class="tip-icon-wrapper">
						<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g clip-path="url(#clip0_3238_3241)">
							<path d="M21.1124 3.16983V3.21291C20.0284 2.4097 18.795 1.82735 17.4823 1.50115C16.0017 1.13322 14.4576 1.10044 12.9627 1.40521C10.9722 1.80733 9.14602 2.79163 7.7158 4.23325C6.28554 5.67492 5.31575 7.50896 4.92949 9.50267H4.92949L4.92926 9.50386C4.64725 10.9984 4.6985 12.5368 5.07934 14.0092C5.46019 15.4817 6.16126 16.852 7.13255 18.0224L7.13245 18.0225L7.13782 18.0285C7.97783 18.9764 8.45968 20.1883 8.49993 21.4542V25.2002C8.49993 26.2611 8.92136 27.2785 9.6715 28.0286C10.4216 28.7788 11.4391 29.2002 12.4999 29.2002H17.4999C18.5608 29.2002 19.5782 28.7788 20.3284 28.0286C21.0785 27.2785 21.4999 26.2611 21.4999 25.2002V21.6913C21.541 20.2857 22.0578 18.9355 22.9658 17.8616L22.9659 17.8617L22.9693 17.8574C24.6672 15.7571 25.4675 13.0717 25.1964 10.3847C24.9253 7.69763 23.6047 5.22617 21.5215 3.50739L21.1124 3.16983ZM22.2499 11.4501L22.2499 11.4516C22.2589 13.1052 21.6914 14.7102 20.645 15.9906C19.4049 17.4622 18.6608 19.2853 18.5152 21.2002H16.4999V17.7002C16.4999 17.3024 16.3419 16.9209 16.0606 16.6396C15.7793 16.3583 15.3978 16.2002 14.9999 16.2002C14.6021 16.2002 14.2206 16.3583 13.9393 16.6396C13.658 16.9209 13.4999 17.3024 13.4999 17.7002V21.2002H11.4921C11.4043 19.3159 10.6825 17.5137 9.44015 16.088C8.63833 15.1266 8.09911 13.9739 7.87503 12.7422C7.65078 11.5096 7.74977 10.2399 8.16234 9.05696C8.57491 7.87402 9.28698 6.81818 10.2291 5.99238C11.1712 5.1666 12.3113 4.59905 13.538 4.34505C14.5923 4.128 15.6817 4.14843 16.7271 4.40486C17.7725 4.6613 18.7478 5.14733 19.582 5.82765C20.4162 6.50798 21.0884 7.36553 21.5499 8.33804C22.0114 9.31056 22.2505 10.3736 22.2499 11.4501ZM18.4999 25.2002C18.4999 25.4654 18.3946 25.7198 18.207 25.9073C18.0195 26.0949 17.7651 26.2002 17.4999 26.2002H12.4999C12.2347 26.2002 11.9804 26.0949 11.7928 25.9073C11.6053 25.7198 11.4999 25.4654 11.4999 25.2002V24.2002H18.4999V25.2002Z" fill="#FFC800" stroke="black" stroke-width="0.5"/>
							</g>
							<defs>
							<clipPath id="clip0_3238_3241">
							<rect width="30" height="30" fill="white" transform="translate(0 0.200195)"/>
							</clipPath>
							</defs>
						</svg>	
					</div>
	
					<span>Tip to improve:</span>
				</div>
			</H3>
			<P2Wrapper>
				<p>
					At the moment, your word stock and understanding of {sortedScoreOfTopics[0].topic} and {sortedScoreOfTopics[1].topic} are good.
					However, to boost your score, you will need to build your vocabulary in {sortedScoreOfTopics[2].topic} and
					{sortedScoreOfTopics[3].topic}.
				</p>
				{#if ["Social Science", "Natural Science", "Humanities"].some(topic => needImprovementTopics.includes(topic))}
				<p>
					You can do it by reading scientific articles. Some of the good sources that we recommend are Guardians, New York Times
					and American Science. Try to pick articles that are related to the topics you are weak at. More importantly, make sure that
					the topic is something you are personally interested in.
				</p>
				{/if}
				{#if needImprovementTopics.includes("Fiction")}
				<p>
					For Fiction, we recommend reading more novels. Some of the books that we recommend are <i>1984</i>, <i>Lord of the Flies</i>,
					<i>The Great Gatsby</i>, and <i>The Call of the Wild</i>. If you are into something shorter, check out 
					<i>I Have No Mouth, and I Must Scream</i> and <i>The Lottery</i>.
				</p>
				{/if}
			</P2Wrapper>
		</div>

		<hr class="hr"/>

		<div class="subsection">
			<H2>5. Mathematics Performace</H2>
			<P2Wrapper>
				<p>
					This is the second half of the test. Like the Reading and Writing section, it has 2
					modules. However, each module has only 22 questions and last 35 minutes. You will have
					access to the graph calculator DESMOS in the test interface, and you are also allowed to
					bring your own calculator.
				</p>
				<p>
					The Math section is designed to assess a student's ability to apply mathematical concepts,
					solve real-world problems, and use critical thinking to interpret data. Approximately 30%
					of the questions will be in-context (also known as word problems, where the questions are
					super long and full of information). While the complexity of word problems have decreased
					compared to the old SAT, they can still be challenging and require a lot of practice.
				</p>
			</P2Wrapper>
		</div>

		<SkillAssessmentChart
			studentPerformances={scoreOfTypes.slice(8)}
			isVerbal={false} 
			--primary-color=var(--light-yellow)
			--secondary-color=var(--light-aquamarine)
		/>

		<div class="tip-section">
			<H3>
				<div class="tip-header">
					<div class="tip-icon-wrapper">
						<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g clip-path="url(#clip0_3238_3241)">
							<path d="M21.1124 3.16983V3.21291C20.0284 2.4097 18.795 1.82735 17.4823 1.50115C16.0017 1.13322 14.4576 1.10044 12.9627 1.40521C10.9722 1.80733 9.14602 2.79163 7.7158 4.23325C6.28554 5.67492 5.31575 7.50896 4.92949 9.50267H4.92949L4.92926 9.50386C4.64725 10.9984 4.6985 12.5368 5.07934 14.0092C5.46019 15.4817 6.16126 16.852 7.13255 18.0224L7.13245 18.0225L7.13782 18.0285C7.97783 18.9764 8.45968 20.1883 8.49993 21.4542V25.2002C8.49993 26.2611 8.92136 27.2785 9.6715 28.0286C10.4216 28.7788 11.4391 29.2002 12.4999 29.2002H17.4999C18.5608 29.2002 19.5782 28.7788 20.3284 28.0286C21.0785 27.2785 21.4999 26.2611 21.4999 25.2002V21.6913C21.541 20.2857 22.0578 18.9355 22.9658 17.8616L22.9659 17.8617L22.9693 17.8574C24.6672 15.7571 25.4675 13.0717 25.1964 10.3847C24.9253 7.69763 23.6047 5.22617 21.5215 3.50739L21.1124 3.16983ZM22.2499 11.4501L22.2499 11.4516C22.2589 13.1052 21.6914 14.7102 20.645 15.9906C19.4049 17.4622 18.6608 19.2853 18.5152 21.2002H16.4999V17.7002C16.4999 17.3024 16.3419 16.9209 16.0606 16.6396C15.7793 16.3583 15.3978 16.2002 14.9999 16.2002C14.6021 16.2002 14.2206 16.3583 13.9393 16.6396C13.658 16.9209 13.4999 17.3024 13.4999 17.7002V21.2002H11.4921C11.4043 19.3159 10.6825 17.5137 9.44015 16.088C8.63833 15.1266 8.09911 13.9739 7.87503 12.7422C7.65078 11.5096 7.74977 10.2399 8.16234 9.05696C8.57491 7.87402 9.28698 6.81818 10.2291 5.99238C11.1712 5.1666 12.3113 4.59905 13.538 4.34505C14.5923 4.128 15.6817 4.14843 16.7271 4.40486C17.7725 4.6613 18.7478 5.14733 19.582 5.82765C20.4162 6.50798 21.0884 7.36553 21.5499 8.33804C22.0114 9.31056 22.2505 10.3736 22.2499 11.4501ZM18.4999 25.2002C18.4999 25.4654 18.3946 25.7198 18.207 25.9073C18.0195 26.0949 17.7651 26.2002 17.4999 26.2002H12.4999C12.2347 26.2002 11.9804 26.0949 11.7928 25.9073C11.6053 25.7198 11.4999 25.4654 11.4999 25.2002V24.2002H18.4999V25.2002Z" fill="#FFC800" stroke="black" stroke-width="0.5"/>
							</g>
							<defs>
							<clipPath id="clip0_3238_3241">
							<rect width="30" height="30" fill="white" transform="translate(0 0.200195)"/>
							</clipPath>
							</defs>
						</svg>	
					</div>
	
					<span>Tip to improve:</span>
				</div>
			</H3>
			<P2Wrapper>
				<p>
					If you find yourself constantly zoning out and losing focus while doing the Math section,
					it is normal. In fact, this phenomenon even has a name, Decision Fatigue. Although the
					Math section is arguably easier, after a long and hard Reading & Writing section, you
					are probably exhausted.
				</p>
				<p>
					To offset this effect, the only way is to build your stamina until you can maintain your
					concentration in two and a half hours. Taking full-length practice tests more frequently
					would be a good training as it resembles the real test. Especially when it's close to your
					exam date, it is recommended that you take a simulation every week.
				</p>
			</P2Wrapper>
		</div>

		<hr class="hr"/>

		<div class="subsection">
			<H2>6. Summary</H2>
			<P2Wrapper>
				<p>
					To know oneself is true progress. From the above insights, now you can have a better idea
					of your strengths and weaknesses.
				</p>
				<p>
					In the coming weeks, your study plan would focus on targeting your weaknesses so you can
					boost your score in the next time you take the simulation/test.
				</p>
			</P2Wrapper>
		</div>

		<H3>Your Strengths</H3>

		<!-- TODO: Strengths -->
		<div class="skill-assessment skill-assessment--strengths">
			{#each strengths as { questionType, review }}
			<div class="skill">
				<div class="skill-icon skill-icon-strength">
					<svg width="18" height="14" viewBox="0 0 18 14" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M3.3158 5.80469L3.31586 5.80462L3.3089 5.79827C3.15075 5.65386 2.96556 5.54165 2.76454 5.46677C2.56355 5.39189 2.34976 5.35545 2.13513 5.35896C1.9205 5.36247 1.70804 5.40589 1.50967 5.48738C1.31127 5.56889 1.13001 5.68727 0.976957 5.83704L0.976956 5.83704C0.823843 5.98687 0.701891 6.16529 0.619523 6.36285C0.537122 6.5605 0.496339 6.77256 0.500258 6.98665C0.504176 7.20072 0.552688 7.41116 0.642145 7.60572C0.73091 7.79877 0.857886 7.97167 1.0145 8.1155L5.38192 12.2438C5.3822 12.2441 5.38248 12.2444 5.38276 12.2446C5.5361 12.3906 5.71692 12.505 5.91415 12.5827C6.11171 12.6605 6.3227 12.7002 6.53519 12.7002C6.74768 12.7002 6.95867 12.6605 7.15623 12.5827C7.35346 12.505 7.53429 12.3905 7.68762 12.2446C7.6879 12.2444 7.68818 12.2441 7.68846 12.2438L16.9703 3.47008C17.1338 3.32554 17.2663 3.14942 17.358 2.95147C17.4513 2.75011 17.5 2.53143 17.5 2.30949C17.5 2.08755 17.4513 1.86888 17.358 1.66752C17.2663 1.4696 17.1339 1.2935 16.9704 1.14897C16.8186 1.00629 16.6404 0.894195 16.4462 0.817734C16.2487 0.739934 16.0377 0.700195 15.8252 0.700195C15.6127 0.700195 15.4017 0.739934 15.2041 0.817734C15.0068 0.895444 14.8259 1.00996 14.6725 1.15599C14.6723 1.15618 14.6721 1.15638 14.6719 1.15657L6.53551 8.85786L3.3158 5.80469Z" fill="#FBDEF0" stroke="black"/>
					</svg>	
				</div>
					
				<div class="skill-text">
					<H4>{questionType}</H4>
					<P2>{review}</P2>
				</div>
			</div>
			{/each}
		</div>

		<H3>Your Weaknesses</H3>

		<!-- Weaknesses -->
		<div class="skill-assessment skill-assessment--weaknesses">
			{#each weaknesses as { questionType, review }}
			<div class="skill">
				<svg class="skill-icon-weakness" width="36" height="37" viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M16.4845 3.32519C17.158 2.15853 18.842 2.15853 19.5155 3.3252L31.6399 24.3252C32.3135 25.4919 31.4715 26.9502 30.1244 26.9502H5.87564C4.52849 26.9502 3.68652 25.4919 4.3601 24.3252L16.4845 3.32519Z" fill="#FFC800" stroke="black" stroke-width="0.5"/>
					<rect x="16.5" y="20.2002" width="3" height="3" rx="1.5" fill="black"/>
					<rect x="16.5" y="9.2002" width="3" height="9" rx="1.5" fill="black"/>
				</svg>	
					
				<div class="skill-text">
					<H4>{questionType}</H4>
					<P2>{review}</P2>
				</div>
			</div>
			{/each}
		</div>

		<hr class="hr"/>

		<div class="subsection">
			<H2>7. Study Plan</H2>
			<P2Wrapper>
				<p>
					The Study Plan below is built to help you improve your score in the coming simulation or
					test, in the most efficient way possible by targetting your mentioned weaknesses. Before
					you proceed, please decide when you would like to retake the simulation or take the
					official test.
				</p>
				<p>
					The timing depends on various factors, for instance, your time left to prepare for the
					test, the difference between your current and your target score, how many full-length
					practice tests you have access to.
				</p>
				<p>
					However, we (and our former students) recommend that if you plan to take the official DSAT
					in one month or less, you should try to take a simulation every week. In fact, try to take
					it every Saturday morning at 9am to prepare yourself mentally as well, two birds with one
					stone.
				</p>
				<p>
					On the other hand, if your planned test day is further than one month away, try to take
					1-2 simulations a month. Seeing your score constantly improving is a great source of
					motivation.
				</p>
				<p>
					Also, the closer the exam date, the more effort you need to put in. That is why the 7-day
					and 14-day study plan are more intensive than the 30-day plan.
				</p>
			</P2Wrapper>
		</div>

		<div class="plan-title">
			<H5>I plan to take the next simulation/test in</H5>
			<div class="select-plan">
				<button class="plan-option" onclick={() => setStudyPlanTime(7)} class:selected-option={studyPlanTime == 7}>
					<H5>7 days</H5>
				</button>
				<button class="plan-option" onclick={() => setStudyPlanTime(14)} class:selected-option={studyPlanTime == 14}>
					<H5>14 days</H5>
				</button>
				<button class="plan-option" onclick={() => setStudyPlanTime(30)} class:selected-option={studyPlanTime == 30}>
					<H5>30 days</H5>
				</button>
			</div>
		</div>

		{#if studyPlanTime != 0}
		<P2>
			Alright, here is your study plan for the next {studyPlanTime} days.
		</P2>

		<StudyPlan simulation={student.simulation} {studyPlan} {studyPlanTime} />
		{/if}
	</div>
	<div class="share-section">
		<H2>Share Your Results</H2>
		<ShareLink />
	</div>
</div>

<style>
	.container {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 4rem;
		padding: 3.4375rem 2rem 0 2rem;
		align-items: center;
		justify-content: center;
		background-color: #F5FDFF;
	}

	.analysis-title {
		font-size: 2rem;
		font-family: 'Inter';
		font-weight: 450;
	}

	.analysis-section {
		width: 100%;
		max-width: 54rem;
		display: flex;
		flex-direction: column;
		gap: 2.5rem;
		border: 1px solid var(--pitch-black);
		border-radius: 1rem;
		padding: 1.875rem;
	}

	.hr {
		width: 100%;
		height: 1px;
		border-top: 1px solid var(--pitch-black);
		background-color: var(--pitch-black);
	}

	.subsection {
		display: flex;
		flex-direction: column;
		gap: 1.25rem;
	}

	.horizontal-section-wrapper {
		display: flex;
		flex-direction: column;
		text-align: center;
		gap: 1.25rem;
	}

	.circle-wrapper {
		display: inline-flex;
		justify-content: center;
		gap: 2rem;
	}

	.tip-section {
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
	}

	.tip-header {
		display: inline-flex;
		gap: 0.625rem;
		align-items: center;
	}

	.tip-icon-wrapper {
		border: 1px solid var(--pitch-black);
		border-radius: 0.3125rem;
		padding: 0.1875rem;
		background-color: var(--light-yellow);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);

		display: flex;
		align-items: center;
		justify-content: center;
	}

	.skill-assessment {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 2rem 1rem;
		padding: 1.875rem;
		border: 1px solid var(--pitch-black);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		border-radius: 0.75rem;
	}

	.skill-assessment--strengths {
		background-color: var(--light-aquamarine);
	}

	.skill-assessment--weaknesses {
		background-color: var(--light-yellow);
	}

	.skill {
		display: inline-flex;
	}

	.skill-icon-strength {
		display: flex;
		align-items: center;
		justify-content: center;

		border-radius: 50%;
		border: 1px solid var(--pitch-black);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		width: 1.6875rem;
		height: 1.6875rem;
		margin-right: 0.5rem;
		background-color: var(--aquamarine);
	}

	.skill-icon-weakness {
		filter: drop-shadow(0.125rem 0.125rem var(--pitch-black));
	}

	.skill-text {
		display: flex;
		flex-direction: column;
		gap: 0.3125rem;
		flex-basis: 100%;
	}

	.plan-title {
		display: inline-flex;
		gap: 1.25rem;
		align-items: center;
	}

	.select-plan {
		display: inline-flex;
		gap: 0.75rem;
	}

	.plan-option {
		padding: 0.875rem 1.275rem; 
		border: 1px solid var(--pitch-black);
		border-radius: 2.1875rem;
		background-color: white;
	}

	.selected-option {
		background-color: var(--sky-blue);
	}

	.share-section {
		padding: 7.25rem 0;
		width: 100%;
		background-color: var(--light-yellow);
		border-top: 1px solid var(--pitch-black);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 1.25rem;

		overflow: hidden;
		white-space: nowrap;
	}	

	@media (max-width: 540px) {
		.wrapper {
			padding: 2rem 1rem;
			overflow: hidden;
		}

		.container {
			padding: 0;
			border: none;
			align-items: center;
		}
	}
</style>
