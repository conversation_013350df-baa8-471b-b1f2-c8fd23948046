<script lang="ts">
    import { <PERSON>, Axis, Spline, Svg, Highlight, Tooltip, <PERSON><PERSON><PERSON>, Points } from 'layerchart';
    import { scaleBand, scaleLinear, scalePoint } from 'd3-scale';
    import { onSnapshot, doc } from 'firebase/firestore';
    import { browser } from '$app/environment';

    import H4 from '$lib/ui/typography/H4.svelte';
    import P1 from '$lib/ui/typography/P1.svelte';
    import { db } from '$lib/firebase/firestore';
    import { user } from '$lib/firebase/auth.svelte';
    import type { CompletedQuestion } from '$lib/types/question.types';
	import { onDestroy } from 'svelte';

    // Loading and error states
    let isLoading = $state(true);
    let error = $state<string | null>(null);

    // Firebase data
    let completedQuestionsData = $state<CompletedQuestion | null>(null);

    // Process Firebase data into chart format
    interface DayData {
        day: string;
        currentPeriod: number;  // Last 7 days (0-6)
        previousPeriod: number; // Previous 7 days (7-13)
        accuracy: number;       // Accuracy for current period
        date: string;           // For tracking actual dates
    }

    // Helper function to get day abbreviation from date
    function getDayAbbreviation(date: Date): string {
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        return days[date.getDay()];
    }

    // Helper function to format date as YYYY-MM-DD
    function formatDate(date: Date): string {
        return date.toISOString().split('T')[0];
    }

    // Helper function to safely create a date from timestamp
    function createSafeDate(timestamp: string): Date {
        const date = new Date(timestamp);
        // Check if the date is valid
        if (isNaN(date.getTime())) {
            return new Date(); // Return current date as fallback
        }
        return date;
    }

    // Helper function to get the last 7 days
    function getLast7Days(): Date[] {
        const days = [];
        const today = new Date();

        // Ensure we have a valid date
        if (isNaN(today.getTime())) {
            console.error('Invalid current date, using fallback');
            const fallbackDate = new Date('2024-01-01'); // Fallback date
            for (let i = 6; i >= 0; i--) {
                const date = new Date(fallbackDate);
                date.setDate(fallbackDate.getDate() - i);
                days.push(date);
            }
            return days;
        }

        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(today.getDate() - i);
            days.push(date);
        }
        return days;
    }

    // Process completed questions data into two 7-day periods chart data
    function processQuestionData(data: CompletedQuestion | null): DayData[] {
        const last7Days = getLast7Days();

        if (!data?.data || !Array.isArray(data.data)) {
            return last7Days.map(date => ({
                day: getDayAbbreviation(date),
                currentPeriod: 0,
                previousPeriod: 0,
                accuracy: 0,
                date: formatDate(date)
            }));
        }

        // Get date ranges for both periods
        const today = new Date();
        const currentPeriodStart = new Date(today);
        currentPeriodStart.setDate(today.getDate() - 6); // Last 7 days (0-6)

        const previousPeriodStart = new Date(today);
        previousPeriodStart.setDate(today.getDate() - 13); // Previous 7 days (7-13)
        const previousPeriodEnd = new Date(today);
        previousPeriodEnd.setDate(today.getDate() - 7);

        // Initialize data structure for both periods
        const dailyData: Record<string, {
            currentPeriod: number;
            previousPeriod: number;
            correct: number;
            total: number
        }> = {};

        last7Days.forEach(date => {
            const dateStr = getDayAbbreviation(date);
            dailyData[dateStr] = { currentPeriod: 0, previousPeriod: 0, correct: 0, total: 0 };
        });

        // Process each completed question
        data.data.forEach(item => {
            // Skip items with invalid or missing timestamps
            if (!item.timestamp) {
                return;
            }

            const questionDate = createSafeDate(item.timestamp);
            const dayOfWeek = getDayAbbreviation(questionDate);

            // Check if question is from current period (last 7 days)
            if (questionDate >= currentPeriodStart && questionDate <= today) {
                dailyData[dayOfWeek].currentPeriod++;
                dailyData[dayOfWeek].total++;
                if (item.wasAnswerCorrect) {
                    dailyData[dayOfWeek].correct++;
                }
            }
            // Check if question is from previous period (days 7-13 ago)
            else if (questionDate >= previousPeriodStart && questionDate < previousPeriodEnd) {
                dailyData[dayOfWeek].previousPeriod++;
            }
        });

        // Convert to chart format with accuracy calculation
        return last7Days.map(date => {
            const dayOfWeek = getDayAbbreviation(date);
            const dayData = dailyData[dayOfWeek];
            return {
                day: dayOfWeek,
                currentPeriod: dayData.currentPeriod,
                previousPeriod: dayData.previousPeriod,
                accuracy: dayData.total > 0
                    ? Math.round((dayData.correct / dayData.total) * 100)
                    : 0,
                date: formatDate(date)
            };
        });
    }

    // Helper function to calculate overall accuracy for the current period
    function calculateOverallAccuracy(data: DayData[]): number {
        const totalQuestions = data.reduce((sum, d) => sum + d.currentPeriod, 0);
        if (totalQuestions === 0) return 0;

        const weightedAccuracy = data.reduce((sum, d) => sum + (d.accuracy * d.currentPeriod), 0);
        return Math.round(weightedAccuracy / totalQuestions);
    }



    // Helper function to calculate percentage change
    function calculatePercentageChange(current: number, previous: number): { value: number; isPositive: boolean; isZero: boolean } {
        if (previous === 0) {
            return { value: current > 0 ? 100 : 0, isPositive: current > 0, isZero: current === 0 };
        }
        const change = ((current - previous) / previous) * 100;
        return {
            value: Math.abs(Math.round(change)),
            isPositive: change > 0,
            isZero: Math.abs(change) < 1
        };
    }

    // Derived values for display
    let dsatData = $derived(processQuestionData(completedQuestionsData));
    let currentPeriodTotal = $derived(dsatData.reduce((sum, d) => sum + d.currentPeriod, 0));
    let previousPeriodTotal = $derived(dsatData.reduce((sum, d) => sum + d.previousPeriod, 0));
    let overallAccuracy = $derived(calculateOverallAccuracy(dsatData));

    // Calculate percentage changes between current and previous periods
    let questionCountChange = $derived(calculatePercentageChange(currentPeriodTotal, previousPeriodTotal));

    // Firebase subscription
    let unsubscribe: (() => void) | null = null;

    $effect(() => {
        if (browser && $user?.uid) {
            isLoading = true;
            error = null;

            const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', 'dataDoc');

            unsubscribe = onSnapshot(
                completedQuestionsRef,
                (snapshot) => {
                    try {
                        if (snapshot.exists()) {
                            completedQuestionsData = snapshot.data() as CompletedQuestion;
                        } else {
                            completedQuestionsData = null;
                        }
                        isLoading = false;
                    } catch (err) {
                        console.error('Error processing question bank data:', err);
                        error = 'Failed to process progress data';
                        isLoading = false;
                    }
                },
                (err) => {
                    console.error('Error fetching question bank progress:', err);
                    error = 'Failed to load progress data';
                    isLoading = false;
                }
            );
        } else if (!$user) {
            // User not logged in
            completedQuestionsData = null;
            isLoading = false;
        }
    });

    onDestroy(() => {
        if (unsubscribe) {
            unsubscribe();
            unsubscribe = null;
        }
    });
</script>


<H4>Question Bank Progress</H4>

{#if isLoading}
    <div class="loading-container">
        <img src="/loading.gif" alt="Loading..." width="128" height="128">
    </div>
{:else if error}
    <div class="error-container">
        <P1>⚠️ {error}</P1>
        <P1>Please try refreshing the page.</P1>
    </div>
{:else}
    <div class="stat-container flex flex-col w-full">
        <div class="flex justify-between flex-row w-full" style="color: var(--sky-blue)">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-this-week"></div>
                <P1 isBold>Last 7 days</P1>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <P1 isBold>{currentPeriodTotal} questions</P1>
                {#if previousPeriodTotal > 0 && !questionCountChange.isZero}
                    <span class="change-indicator" class:positive={questionCountChange.isPositive} class:negative={!questionCountChange.isPositive}>
                        {questionCountChange.isPositive ? '↗' : '↘'} {questionCountChange.value}%
                    </span>
                {/if}
            </div>
        </div>
        <div class="flex justify-between flex-row w-full" style="color: var(--aquamarine)">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-accuracy"></div>
                <P1 isBold>Acc. rate</P1>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <P1 isBold>{overallAccuracy}%</P1>
            </div>
        </div>
        <div class="flex justify-between flex-row w-full" style="color: #A7A7A7;">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-last-week"></div>
                <P1 isBold>Previous 7 days</P1>
            </div>
            <P1 isBold>{previousPeriodTotal} questions</P1>
        </div>
    </div>
{/if}

{#if !isLoading && !error && $user}
    <div class="dsat-chart h-[300px] grid p-4">
        <div class="col-start-1 row-start-1 z-0">
            <BarChart
                data={dsatData}
                x="day"
                xScale={scaleBand().domain(dsatData.map(d => d.day)).padding(0.5)}
                series={[
                    { key: "currentPeriod", color: "var(--sky-blue)" },
                    { key: "previousPeriod", color: "#A7A7A7" }
                ]}
                seriesLayout="group"
                props={{
                    xAxis: { format: "none" },
                    yAxis: { format: "metric" },
                    tooltip: { header: { format: "none" } },
                }}
                padding={{ left: 16, bottom: 24 }}
            />
        </div>

        <div class="col-start-1 row-start-1 z-10">
            <Chart
                data={dsatData}
                x="day"
                xScale={scalePoint().domain(dsatData.map(d => d.day)).padding(0.75)}
                y="accuracy"
                yDomain={[0, 100]}
                padding={{ left: 16, bottom: 24 }}
                tooltip={{ mode: "band" }}
                let:height
            >
                <Svg>
                    <Axis
                        placement="right"
                        scale={scaleLinear([0, 100], [height, 0])}
                        format={(v: number) => `${v}%`}
                    />
                    <Spline class="stroke-2" style="stroke: var(--aquamarine)" />
                    <Points class="fill-primary stroke-primary" />
                    <Highlight points lines />
                </Svg>

                <Tooltip.Root let:data classes={{ root: "bg-white rounded-md border border-black shadow-[0.125rem_0.125rem_0_#000000]" }}>
                    <Tooltip.Header>{data.day}</Tooltip.Header>
                    <Tooltip.List>
                        <Tooltip.Item label="Last 7 days" value={data.currentPeriod} format="metric" />
                        <Tooltip.Item label="Previous 7 days" value={data.previousPeriod} format="metric" />
                        <Tooltip.Item label="Accuracy" value={`${data.accuracy}%`} />
                    </Tooltip.List>
                </Tooltip.Root>
            </Chart>
        </div>
    </div>
{/if}

  


<style>
    * {
        --color-primary: 157, 79%, 63%; /* aquamarine in HSL */
        --color-secondary: 100, 50%, 50%;
    }
    
    .dsat-chart {
        width: 100%;
    }

    .legend-this-week {
        width: 8px;
        height: 16px;
        flex-shrink: 0;
        border: 1px solid #000;
        background: var(--sky-blue, #66E2FF);
    }

    .legend-accuracy {
        width: 10px;
        height: 10px;
        flex-shrink: 0;
        background: var(--aquamarine);
        border-radius: 5px;
    }

    .legend-last-week {
        width: 8px;
        height: 16px;
        flex-shrink: 0;
        border: 1px solid #000;
        background: #A7A7A7;
    }

    .loading-container,
    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        text-align: center;
        min-height: 200px;
    }

    .error-container {
        color: var(--rose, #EB47AB);
    }

    .change-indicator {
        font-size: 0.875rem;
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        border: 1px solid;
        background: var(--white);
        white-space: nowrap;
    }

    .change-indicator.positive {
        color: var(--aquamarine);
        border-color: var(--aquamarine);
        background: var(--light-aquamarine);
    }

    .change-indicator.negative {
        color: var(--rose);
        border-color: var(--rose);
        background: var(--light-rose);
    }
</style>