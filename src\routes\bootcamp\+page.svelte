<script>
    import { H2, P2 } from "$lib/ui";
</script>

<div class="container">
    <H2>Hello!</H2>
    <div class="passage">
        <P2>
            This is DSAT16, the first Digital SAT online course in Vietnam.
        </P2>
        <P2>
            On February 25, 2022, College Board officially announced that the SAT would become a fully computer-based exam (also known as Digital SAT), starting from March 2023 for international students, including Vietnam.
        </P2>
        <P2>
            Besides the format, exam duration, test structure, and question difficulty will also change, with the most notable being the "adaptive" nature of the test, meaning the difficulty of the later section will depend on the student's performance in the previous section.
        </P2>
        <P2>
            In the first Digital SAT exam in Vietnam in March 2023, I achieved a perfect score of 1600/1600. I realized that by understanding the core concepts and creating specific strategies for each question type, rather than just practicing endlessly, scores can improve rapidly. Subsequently, I decided to launch DSAT16, a course that includes tips and strategies for studying and test-taking that I've developed during my 6 months of self-study.
        </P2>
        <P2>
            The course includes:
        </P2>
        <ul>
            <li><P2>Video: lectures about the tips and strategies I applied while studying and taking the test,</P2></li>
            <li><P2>Notes: the same content but in text format,</P2></li>
            <li><P2>Practice Arena: a set of exercises I've created to help you practice the tips and strategies learned,</P2></li>
            <li><P2>Simulation: a practice room with an interface similar to the official Digital SAT exam to help you become psychologically comfortable with the test format.</P2></li>
        </ul>
    </div>
</div>

<svelte:head>
    <title>Study - DSAT16</title> 
</svelte:head>

<style>
    .container {
        margin-left: 24rem;
        margin-top: 3rem;
    }

    .passage {
        margin-top: 2rem;
        max-width: 62.5rem;
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
    }

    ul {
        list-style-type: circle;
        margin-left: 2.5em;
        padding-left: 0;
        margin-top: -1.25rem;
        margin-bottom: 1.25rem;
    }

    li {
        color: #000;
        font-family: "Open Sans";
        font-size: 1.125rem;
        font-style: normal;
        font-weight: 450;
        line-height: 1.6875rem;
    }

    @media (max-width: 1023px) {
        .container {
            margin-left: 0;
            margin-top: 5rem;
            padding: 0 1rem;
        }
    }
</style>