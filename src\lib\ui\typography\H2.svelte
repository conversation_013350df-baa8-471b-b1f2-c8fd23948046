<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<!-- 
    @component
    A styled h2 element.
    
    Usage:
    ```tsx
    <H2>Heading 2</H2>
    ```
-->

<h2>
    {@render children?.()}
</h2>

<style>
    h2 {
        font-family: "Inter";
        font-size: 2.25rem;
        font-weight: 600;
    }

    @media (max-width: 540px) {
        h2 {
            font-size: 1.875rem;
        }
    }
</style>
