<script>
    /** @type {{isWalkthrough?: boolean, children?: import('svelte').Snippet}} */
    let { isWalkthrough = false, children } = $props();
</script>

<div class="right" class:walk={isWalkthrough}>
    {@render children?.()}
</div>

<style>
    .right {
        display: flex;
        /* justify-self: end; */
        flex-direction: column;
        margin: 32px 64px 0 0;
        max-width: 651px;
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .walk {
        margin: 32px 64px 16px 16px;
    }
</style>

