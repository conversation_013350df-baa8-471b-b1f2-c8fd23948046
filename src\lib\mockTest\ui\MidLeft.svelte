<script>
    /** @type {{isSPR: any, isWalkthrough?: boolean, children?: import('svelte').Snippet}} */
    let { isSPR, isWalkthrough = false, children } = $props();
</script>

<div class="left" class:left--spr={isSPR && !isWalkthrough} class:left--walk={isWalkthrough}>
    {@render children?.()}
</div>

<style>
    .left {
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;

        margin: 32px 0 16px 64px;
        gap: 0;
        display: flex;
        flex-direction: column;
    }

    .left:last-child {
        margin-bottom: 32px;
    }

    .left--spr {
        gap: 6px;
        margin: 16px 0;
    }

    .left--walk {
        margin: 32px 16px 16px 32px;
        gap: 6px;
    }
</style>