<!-- 
    @component
    ## WalkReview
    This component is used to display the review of the test. It shows the number of questions answered correctly, incorrectly and unanswered. It also shows the list of questions with the correct, incorrect and unanswered status. The user can click on the question to navigate to the question.
    
    ## Props
    - `data`: The data object containing the test details.
    - `setQuestion`: A function to set the question to be displayed.
    - `isMinitest`: A boolean to check if the test is a minitest or not.
-->

<script lang="ts">
    interface Props {
        student: any;
        test: any;
        setQuestion: any;
        isMinitest?: boolean;
        m?: any;
        children?: import('svelte').Snippet;
    }

    let {
        student,
        test,
        setQuestion,
        isMinitest = false,
        m = $bindable(-1),
        children
    }: Props = $props();

    function previousModule() {
        m--;
    }

    function nextModule() {
        m++;
    }

    const NUMBER_OF_PRIOR_QUESTIONS = [0, 27, 54, 76];

    function isCorrect(question, index) {
        const i = index + (NUMBER_OF_PRIOR_QUESTIONS[m] ?? 0);
        const correctAnswer = question.correctAnswer;
        if (typeof correctAnswer === 'object' && student.answers[i]) {
            return (correctAnswer.recommended.concat(correctAnswer.possible)).some(answer => student.answers[i] == answer 
            || ( student.answers[i].includes('/') && (student.answers[i].split('/')[0] / student.answers[i].split('/')[1]) == answer )) 
        } else {
            return correctAnswer === student.answers[i];
        }
    }
</script>

<div class="middle-container" class:middle-full={isMinitest}>
    <div class="middle">
        <div class="check">You Have Submitted {test.title}</div>
        <div class="on">You got <span class="bold">{m == -1 ? student.score : student.rawModuleScores.reduce((a, b) => a + b)}/{student.answers.length}</span> questions right,
            {#if m != -1}with an estimated score of <span class="bold">{student.predictedTotalScore}/1600</span>.{/if}
            Well Done!
        </div>
        <div class="for">You can check your answers and detailed solutions by clicking each question below.</div>
        <div class="review-nav">
            {#if m != -1}
                {#if m != 0}
                <button aria-label="arrow left" class="arrow-left" onclick={previousModule}>
                    <svg width="43" height="73" viewBox="0 0 43 73" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.44272 40.529L34.4991 72.5853L42.5117 64.5726L14.4617 36.5226L42.5117 8.47263L34.4991 0.459961L2.44272 32.5163C1.38038 33.5789 0.783596 35.02 0.783596 36.5226C0.783596 38.0252 1.38038 39.4663 2.44272 40.529Z" fill="#66E2FF"/>
                    </svg>                        
                </button>
                {/if}

                {#if m != 3}
                <button aria-label="arrow right" class="arrow-right" onclick={nextModule}>
                    <svg width="43" height="73" viewBox="0 0 43 73" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M40.5573 40.529L8.50095 72.5853L0.488281 64.5726L28.5383 36.5226L0.488281 8.47263L8.50095 0.459961L40.5573 32.5163C41.6196 33.5789 42.2164 35.02 42.2164 36.5226C42.2164 38.0252 41.6196 39.4663 40.5573 40.529Z" fill="#66E2FF"/>
                    </svg>
                </button>
                {/if}
            {/if}
            <div class="title-and-icon">
                <div class="title">{test.title}</div>
                <div class="icons">
                    <div class="unanswered">
                        <div class="unanswered-icon correct-icon"></div>
                        <div class="unanswered-text">Correct</div>
                    </div>
                    <div class="unanswered">
                        <div class="unanswered-icon incorrect-icon"></div>
                        <div class="unanswered-text">Incorrect</div>
                    </div>
                    <div class="unanswered">
                        <div class="unanswered-icon"></div>
                        <div class="unanswered-text">Unanswered</div>
                    </div>
                </div>
            </div>
            <div class="nav-answer-list">
                {#each test.questions as question, index}
                    <div class="answer-box-container">
                        {#if student.marked[index + (NUMBER_OF_PRIOR_QUESTIONS[m] ?? 0)]}
                            <svg class="marked-icon" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                <path d="M10 1.25H5C3.9375 1.25 3.125 2.0625 3.125 3.125V13.125C3.125 13.25 3.125 13.3125 3.1875 13.4375C3.375 13.75 3.75 13.8125 4.0625 13.6875L7.5 11.6875L10.9375 13.6875C11.0625 13.75 11.125 13.75 11.25 13.75C11.625 13.75 11.875 13.5 11.875 13.125V3.125C11.875 2.0625 11.0625 1.25 10 1.25Z" fill="#FF66C4"/>
                            </svg>
                        {/if}
                        <button class={`answer-box ${isCorrect(question, index) ? "correct-icon" : student.answers[index + (NUMBER_OF_PRIOR_QUESTIONS[m] ?? 0)] === null ? "" : "incorrect-icon"}`} onclick={() => setQuestion(index)}>{index + 1}</button>    
                    </div>
                {/each}
            </div>
        </div>
        {@render children?.()}
    </div>
</div>



<style>
    button {
        border: 0;
        background: none;
    }

    button:hover {
        opacity: 1 !important;
    }

    .middle-container {
        display: flex;
        align-items: center;
        width: 100%;
        height: calc(100vh - 180px);
        flex-direction: column;
        overflow: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .middle:last-child {
        margin-bottom: 64px;
    }

    .middle-full {
        height: 100%;
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }

    .middle {
        min-width: 650px;
        max-width: 840px;
        width: 70%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .check {
        margin-top: 55px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .on {
        margin-top: 33px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .for {
        margin-top: 13px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 16px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
        text-decoration: underline;
    }

    .review-nav {
        position: relative;
        margin: 30px 0;
        display: inline-flex;
        padding: 35px;
        flex-direction: column;
        gap: 40px;
        align-items: center;
        border-radius: 8px;
        background: #FFF;
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.25);
    }

    .title-and-icon {
        width: 100%;
        display: flex;
        padding-bottom: 26px;
        justify-content: space-between;
        align-items: flex-end;
        border-bottom: 1px solid #B2B2B2;
    }

    .title {
        color: #000;
        font-family: "Inter";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .icons {
        display: flex;
        align-items: flex-start;
        gap: 17px; 
    }

    .unanswered {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
    }

    .unanswered-icon {
        width: 22px;
        height: 22px;
        border: 0.75px dashed #000;
    }

    .correct-icon {
        background: var(--web-color-light-aquamarine, #D1FFEE);
    }

    .incorrect-icon {
        background: var(--web-color-light-rose, #FFDAF1);
    }

    .unanswered-text {
        color: #000;
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    span.bold {
        font-weight: 700;
    }

    .nav-answer-list {
        display: flex;
        align-items: flex-end;
        align-content: flex-end;
        gap: 25px 35px;
        flex-wrap: wrap;
    }

    .answer-box-container {
        position: relative;
    }

    .marked-icon {
        position: absolute;
        right: -7px;
        top: -6.5px;
        z-index: 1;
    }

    .answer-box {
        width: 45px;
        height: 45px;
        flex-shrink: 0;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 25px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        border: 1px dashed #000;
    }

    .arrow-left {
        position: absolute;
        left: -10%;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
    }

    .arrow-right {
        position: absolute;
        right: -10%;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
    }
</style>