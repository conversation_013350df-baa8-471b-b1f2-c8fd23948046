export const note = {
    slug: '2',
    title: 'Lecture W2: Notes',
    hyperlink: [], // TODO
    notes: [
        // Punctuation - Welcome!
        {
            type: 'Section',
            title: {
                type: 'Heading 1',
                content: 'Punctuation - Welcome!'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Last session, we discussed the non-grammar questions (Transitions and Student’s Notes). While being in the Writing section, They are not testing your grammar ability.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'In this session, we will look at punctuation, which is arguably easier since it does not involve any critical thinking and, instead, test your ability to correctly use punctuation to organize and share your ideas.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'However,  it is possible for incorrect answer choices to appear deceptively correct. This is done deliberately to test your understanding of the rules and your ability to identify subtle errors.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'This lecture will familiarize yourself with the basic rules of Punctuation as well as giving you some tips to avoid falling into such traps.'
                }
            ]
        },

        // Concepts
        {
            type: 'Section',
            title: {
                type: 'Heading 1',
                content: 'Concepts'
            },
            content: [
                {
                    type: 'Paragraph',
                    content: 'The SAT primarily tests your knowledge of 6 punctuations:'
                },
                {
                    type: 'List',
                    content: [
                        'Period (.)',
                        'Semicolons (;)',
                        'Colons (:)',
                        'Commas (,)',
                        'Dashes (—)',
                        'Parentheses (())'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Punctuation marks are used to separate sentences, clauses, phrases, and words.'
                },
                {
                    type: 'List',
                    content: [
                        'They are also used to indicate pauses, emphasis, and tone. This usage is not the focus of the dSAT, however.'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Punctuation is an important part of writing. It helps to make writing clear, concise, and easy to read. When punctuation is used correctly, it can help to improve the overall readability of a text.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'The use of punctuation is defined by a set of rules. For example, separating a sentence from a word would require a different mark compared to separating a sentence from a sentence. As a result, to know how to use punctuation properly, we must first know how to differentiate between clauses.'
                }
            ]
        },

        // Conjunction
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Conjunction'
            },
            content: [
                {
                    type: 'Paragraph',
                    content: 'Conjunctions are words used to connect clauses (so basically transitions).'
                },
                {
                    type: 'Paragraph',
                    content:
                        'There are 3 types of Conjunction (Coordinating, Subordinating, Correlative). However, the dSAT only focuses on the first 2:'
                },
                {
                    type: 'List',
                    content: [
                        '<b>Coordinating Conjunctions</b> consist of <b>FANBOYS</b> (<i>“for”, “and”, “nor”, “but”, “or”, “yet”</i>, and <i>“so”</i>).'
                    ]
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Most e-readers interfere with intuitive navigation of a text <b>and</b> inhibit people from mapping the journey in their minds.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List',
                    content: [
                        '<b>Subordinating Conjunctions</b> can signal a cause-and-effect relationship, a contrast, or some other kind of relationship between the clauses. '
                    ]
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'The brain essentially regards letters as physical objects <b>because</b> it does not really have another way of understanding them.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Independent Clause
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Independent Clause'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Even though the dSAT doesn’t test your ability to identify a statement as either a sentence or a non-sentence, it is still a crucial ability to have.'
                },
                {
                    type: 'Paragraph',
                    content: 'A sentence is made out of at least <b>A Subject</b> and <b> Predicate.</b>'
                },
                {
                    type: 'List',
                    content: [
                        'The subject is the person or thing that the sentence is about. It can be a noun, pronoun, or noun phrase.',
                        'The predicate is the part of the sentence that tells what the subject is doing or what is being done to it. It always includes a verb.'
                    ]
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'The athlete runs.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List',
                    content: [
                        "<i>'The athlete'</i> is the subject.",
                        "<i>'Runs'</i> is the predicate. In this case, it only contains a verb."
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Because it can stand alone, a sentence is also known as an <b>Independent Clause</b>. An independent clause usually ends with a period or a semicolon.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'The athlete runs.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'The athlete runs;'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'A comma cannot be used to separate 2 independent clauses. Doing this would cause a comma splice error.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'E-ink is easy on the eyes because it reflects ambient light just like a paper '
                                },
                                {
                                    type: 'card-incorrect',
                                    content: 'book, computer'
                                },
                                {
                                    type: 'card-normal',
                                    content: " screens shine light directly into people's faces."
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Note that a statement’s meaning doesn’t have anything to do with whether it is a sentence or not. Although the following statement doesn’t make sense out of context, it is still a sentence.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'All of them went to the store.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Dependent Clause
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Dependent Clause'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Dependent Clauses are clauses that can’t stand alone like a sentence. Instead, they must go with a sentence to add more information to it.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'As far as our brains are concerned'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'This tells us how our brains view the subject. However, it does not make sense on its own and needs to be attached to an independent clause to get its meaning.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'As far as our brains are concerned, text is a tangible part of the physical world we inhabit.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content: 'There are 2 types of dependent clauses:'
                },
                {
                    type: 'List',
                    content: [
                        '<b>Essential Clauses</b> are dependent clauses that provide necessary information about the noun or pronoun it modifies in the sentence. This means that the clause is necessary to the meaning of the sentence.'
                    ]
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'The astronaut <b>who first walked on the Moon</b> was Neil Armstrong.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List Lv2',
                    content: ['Essential Clauses should not be separated from the sentence.']
                },
                {
                    type: 'List',
                    content: [
                        '<b>Non-essential Clauses</b> provide additional or non-essential information about the noun they modify. This information is not necessary to understand the meaning of the sentence, but it can add interest or detail.'
                    ]
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Recent surveys suggest that although most people still prefer paper<b>—especially when reading intensively—</b>attitudes are changing.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List Lv2',
                    content: [
                        'Non-essential Clauses must be separated from the sentence by commas, dashes or parentheses.'
                    ]
                },
                {
                    type: 'Paragraph',
                    content: 'Below are a few ways a dependent clause can start off with.'
                }
            ]
        },

        // Relative Clause
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Relative Clause'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Dependent clauses are typically introduced by relative pronouns, such as <i>“who”, “which”</i>, or <i>“that”.</i>'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Students <b>who read the texts on computers</b> performed a little worse than those who read on paper.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Although a digital text has a length<b>—which is sometimes represented with a scroll or progress bar—</b>it has no obvious shape or thickness.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content: 'Clauses that start with that are almost always essential (like this one).'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Jaejeung Kim and his colleagues have designed an innovative and unreleased interface <b>that makes iBooks seem primitive.</b>'
                                }
                            ]
                        },
                        {
                            type: 'Card Title',
                            content: 'Exception:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Magazines are now useless and impossible to understand for digital natives<b>—that is,</b> for people who have been interacting with digital technologies from a very early age.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Subordinate Clause
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Subordinate Clause'
            },
            content: [
                {
                    type: 'Paragraph',
                    content: 'A dependent clause can also be introduced by a subordinating conjunction.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        '<b>Because it does not really have another way of understanding them,</b> the brain essentially regards letters as physical objects.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Notice that when removing the conjunction, the phrase becomes an independent clause.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'When the independent clause comes before the subordinate clause, the comma is unnecessary. This construction will appear in the passage. However, you will not be tested on it; just know that it is acceptable.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'The brain essentially regards letters as physical objects <b>because it does not really have another way of understanding them.</b>'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Prepositional Clause
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Prepositional Clause'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Some dependent clauses also start with prepositions, which are words that describe time or location.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'We did not invent writing <b>until</b> relatively recently <b>in</b> our evolutionary history.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Appositive
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Appositive'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'An appositive is a noun or pronoun — often with modifiers — set beside another noun or pronoun to explain or identify it.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'John Kennedy<b>, the popular US president,</b> was known for his eloquent and inspirational speeches.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Participial Phrase
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Participial Phrase'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'A participle is an adjective form of a verb. Every verb has two participles: Present Participle (-ing) and Past Participle (usually by adding -ed).'
                },
                {
                    type: 'Paragraph',
                    content: 'A participial phrase is a phrase that begins with a participle.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        '<b>Depending</b> on the model of the device, glare, pixilation and flickers can tire the eyes.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        '<b>Compared</b> with paper, screens may drain more of our mental resources while we are reading and make it a little harder to remember what we read when we are done.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Just keep in mind that not every word that ends with -ing is a verb. A lot of students had mistaken this as a sentence before.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Polls and consumer reports <b>indicating</b> that modern screens and e-readers fail to adequately recreate certain tactile experiences of reading on paper'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Because <i>“indicating”</i> looks like a verb, most people think that this is a sentence. However, it is actually a fragment.'
                }
            ]
        },

        // Punctuations
        {
            type: 'Section',
            title: {
                type: 'Heading 1',
                content: 'Punctuations'
            },
            content: [
                {
                    type: 'Paragraph',
                    content: 'To reiterate, the SAT primarily tests your knowledge of 6 punctuations:'
                },
                {
                    type: 'List',
                    content: [
                        'Period (.)',
                        'Semicolons (;)',
                        'Colons (:)',
                        'Commas (,)',
                        'Dashes (—)',
                        'Parentheses (())'
                    ]
                }
            ]
        },

        // Periods
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Periods'
            },
            content: [
                {
                    type: 'Paragraph',
                    content: 'Periods are used to join sentences (or independent clauses).'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'When it comes to intensively reading long pieces of plain text, paper and ink may still have the '
                                },
                                {
                                    type: 'card-correct',
                                    content: 'advantage. Text'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' is not the only way to read, however.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content: 'Without the period, the result is a run-on sentence and is not acceptable:'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'When it comes to intensively reading long pieces of plain text, paper and ink may still have the '
                                },
                                {
                                    type: 'card-incorrect',
                                    content: 'advantage text'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' is not the only way to read, however.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Semicolons
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Semicolons'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Like periods, semicolons are used to join sentences. When used this way, they are interchangeable for the purpose of the SAT.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'When it comes to intensively reading long pieces of plain text, paper and ink may still have the '
                                },
                                {
                                    type: 'card-correct',
                                    content: 'advantage; text'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' is not the only way to read, however.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'In addition, semicolons can also be used to separate list items, especially when they are long sentences or already contain a comma.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'In the end, the meal Mabel and Harry shared was first-rate: the bread and cabbage dish complemented the goulash exquisitely; the chocolate croquembouche was divine; and the conversation was lively and amicable.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Colons
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Colons'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Colons are used to introduce an explanation, a result of the previous clause, or a list.'
                },
                {
                    type: 'List',
                    content: ['Explanation:']
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'A proof assistant can have some drawbacks: It often complains that it does not understand the definitions, axioms or reasoning steps entered by the mathematician.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List Lv2',
                    content: [
                        'Here, the clause before the colon states that a proof assistant has drawbacks, and the clause after it explains what the drawbacks are.'
                    ]
                },
                {
                    type: 'List',
                    content: ['List:']
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'When the first farmers arrived in Europe from Turkey about 8,000 years ago, three large groups of hunter-gatherers thrived across Europe<b>: the Iberians, the Oberkassel, and the Sidelkino.</b>'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List',
                    content: ['Result:']
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'The economic reforms led to an unprecedented increase in foreign investments: a 35% rise in foreign direct investment was recorded within the first quarter.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'The clause appears before a colon must be an independent clause. However, The clause after it can be either independent or dependent.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Mathematicians are grappling with the latest transformative force: artificial intelligence.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content: 'Consequently, constructions like these will always be wrong:'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'Mathematicians are grappling with the latest transformative force '
                                },
                                {
                                    type: 'card-incorrect',
                                    content: 'such as:'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' artificial intelligence.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'Mathematicians are grappling with the latest transformative force '
                                },
                                {
                                    type: 'card-incorrect',
                                    content: 'including:'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' artificial intelligence.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Commas
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Commas'
            },
            content: [
                {
                    type: 'Paragraph',
                    content: 'Commas can be used to link dependent clauses to independent clauses.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'As digital texts and technologies become more prevalent, we gain new and more mobile ways of reading.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List',
                    content: [
                        'Sometimes, if the independent clause comes first, a comma is not necessary. However, this will not be tested in the exam.'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'It can also work along with coordinating conjunctions (<b>FANBOYS</b>) to link 2 independent clauses.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        "E-ink is easy on the eyes because it reflects ambient light just like a paper book<b>, but</b> computer screens shine light directly into people's faces."
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List',
                    content: [
                        'This construction is interchangeable with a period or a semicolon (Comma + FANBOYS = Period = Semicolon).'
                    ]
                },
                {
                    type: 'Paragraph',
                    content: 'In a list with 3 or more items, the items are separated with commas.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'When the first farmers arrived in Europe from Turkey about 8,000 years ago, three large groups of hunter-gatherers thrived across Europe<b>: the Iberians, the Oberkassel, and the Sidelkino.</b>'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List',
                    content: [
                        'The last comma in the list is not required. This is also known as an Oxford Comma and is the only scenario where comma + FANBOYS ≠ Period.'
                    ]
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'When the first farmers arrived in Europe from Turkey about 8,000 years ago, three large groups of hunter-gatherers thrived across Europe: '
                                },
                                {
                                    type: 'card-correct',
                                    content: 'the Iberians, the Oberkassel and the Sidelkino.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'List',
                    content: ['If the list only has 2 items, no commas should be used.']
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Surveys indicate that screens and e-readers interfere with two other important aspects of navigating texts: '
                                },
                                {
                                    type: 'card-incorrect',
                                    content: 'serendipity, and a sense of control.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'It is also used to separate non-essential clauses from the rest of the sentence.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Surveys and consumer reports also suggest that the sensory experiences typically associated with reading<b>, especially tactile experiences,</b> matter to people more than one might assume.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Dashes & Parentheses
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Dashes & Parentheses'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Like commas, dashes and parentheses can be used to separate independent clauses from dependent clauses.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Surveys and consumer reports also suggest that the sensory experiences typically associated with reading<b>—especially tactile experiences—</b>matter to people more than one might assume.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Surveys and consumer reports also suggest that the sensory experiences typically associated with reading<b>(especially tactile experiences)</b>matter to people more than one might assume.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'For the purpose of the SAT, two commas, two dashes, and two parentheses are interchangeable, except for transitions, where only commas are acceptable.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'The book that I read last night '
                                },
                                {
                                    type: 'card-correct',
                                    content: '- which was about a young woman who travels to a faraway land -'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' was very exciting.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'The book that I read last night '
                                },
                                {
                                    type: 'card-correct',
                                    content: '(which was about a young woman who travels to a faraway land)'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' was very exciting.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content: 'The book that I read last night'
                                },
                                {
                                    type: 'card-correct',
                                    content: ', which was about a young woman who travels to a faraway land,'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' was very exciting.'
                                }
                            ]
                        },
                        {
                            type: 'Card Title',
                            content: 'Exception:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Before 1992, most studies concluded that people read slower, less accurately and less comprehensively on screens than on paper. Studies published since the early 1990s '
                                },
                                {
                                    type: 'card-incorrect',
                                    content: '(however)'
                                },
                                {
                                    type: 'card-normal',
                                    content: ' have produced more inconsistent results.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'In addition, a dash can be used in place of a colon to introduce an explanation or a list.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'We often think of reading as a cerebral activity concerned with the abstract'
                                },
                                {
                                    type: 'card-correct',
                                    content: '—thoughts and ideas, tone and themes, metaphors and motifs.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'We often think of reading as a cerebral activity concerned with the abstract'
                                },
                                {
                                    type: 'card-correct',
                                    content: ': thoughts and ideas, tone and themes, metaphors and motifs.'
                                }
                            ]
                        }
                    ]
                }
            ]
        },

        // Common Motifs in the Test
        {
            type: 'Section',
            title: {
                type: 'Heading 1',
                content: 'Common Motifs in the Test'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'In this section, we will be going through various motifs that the test might use to test your knowledge about punctuation, along with ways to overcome them.'
                }
            ]
        },

        // Comma + FANBOYS = Period = Semicolon
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Comma + FANBOYS = Period = Semicolon'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Because there can only be 1 correct answer, if at least 2 of these constructions appear in the answer choices, you can eliminate all of them.'
                },
                {
                    type: 'Question',
                    content: {
                        id: 0,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'From afar, African American fiber artist Bisa Butler’s portraits look like paintings, their depictions of human faces, bodies, and clothing so intricate that it seems only a fine brush could have rendered them. When viewed up close, however, the portraits reveal themselves to be __________ stitching barely visible among the thousands of pieces of printed, microcut fabric.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: ['quilts, and the', 'quilts, the', 'quilts; the', 'quilts. The'],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content:
                        'Since (A), (C), and (D) are interchangeable, they can all be eliminated. That leads to (B). To double check, we can see that the comma is being used to separate the nonessential information <i>“the stitching barely visible among the thousands of pieces of printed, microcut fabric”.</i>'
                }
            ]
        },

        // Commas Around Names and Relative Clauses
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Commas Around Names and Relative Clauses'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Oftentimes, you will be asked whether to put commas around names or not (in other words, whether they are essential or non-essential). Both options are grammatically acceptable, so it solely depends on the context.'
                },
                {
                    type: 'Card',
                    content: [
                        {
                            type: 'Card Title',
                            content: 'Example:'
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Writers, who tend to procrastinate, should not be given open deadlines.'
                                }
                            ]
                        },
                        {
                            type: 'Card Paragraph',
                            content: [
                                {
                                    type: 'card-normal',
                                    content:
                                        'Writers who tend to procrastinate, should not be given open deadlines.'
                                }
                            ]
                        }
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'This sentence can work both ways because its meaning changes based on the structure. The first constructions suggest that all writers tend to procrastinate. The 2nd one, on the other hand, implies that only some writers procrastinate.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'The most straightforward way to determine is to cross the name out and see if the sentence still makes sense without it or not. If it does, the commas are required; if it does not, the commas are unnecessary.'
                },
                {
                    type: 'Paragraph',
                    content: 'Let’s look at an example:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 1,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'Harper Lee was an American novelist known for her writing of courage, compassion and justice. Her best seller ______ sold over 40 million copies worldwide and is considered a classic of American literature.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: [
                            'to kill a mockingbird',
                            'to kill a mockingbird,',
                            ', to kill a mockingbird,',
                            ', to kill a mockingbird'
                        ],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content:
                        'In this case, we only need to know that the sentence is about her best seller: the name of the best seller is non-essential. In addition, logically, she only has 1 best seller. Therefore, there’s no need to specify its name. Putting 2 commas around the name like in (C) is the correct construction.'
                },
                {
                    type: 'Paragraph',
                    content: 'Let’s try another one:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 2,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'The city of Pompeii, which was buried in ash following the eruption of Mount Vesuvius in 79 CE, continues to be studied by archaeologists. Unfortunately, as ________ attest, archaeological excavations have disrupted ash deposits at the site, causing valuable information about the eruption to be lost.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: [
                            'researchers, Roberto Scandone and Christopher Kilburn,',
                            'researchers, Roberto Scandone and Christopher Kilburn',
                            'researchers Roberto Scandone and Christopher Kilburn',
                            'researchers Roberto Scandone, and Christopher Kilburn'
                        ],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content:
                        'Surrounding their names with commas would suggest that Roberto Scandone and Christopher Kilburn are the only 2 researchers in the world. Logically, that is definitely not the case. Therefore, no comma is required. (C) is the correct answer.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'When you are sure that the name is essential, you should still check whether a comma is needed for any other reason:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 3,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'The city of Pompeii, which was buried in ash following the eruption of Mount Vesuvius in 79 CE, continues to be studied by archaeologists. Unfortunately, as ________ two prominent figures in the study of volcanic eruptions, attest, archaeological excavations have disrupted ash deposits at the site, causing valuable information about the eruption to be lost.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: [
                            'researchers, Roberto Scandone and Christopher Kilburn,',
                            'researchers Roberto Scandone and Christopher Kilburn',
                            'researchers Roberto Scandone and Christopher Kilburn,',
                            'researchers Roberto Scandone, and Christopher Kilburn,'
                        ],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content:
                        'As we have established, their names are essential. However, at the end of the blank is a non-essential clause. Therefore, a comma is needed at the end. (C) is the correct answer.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'Note: from the 4 official practice tests, no commas tends to be the right answer for these questions. You should still be careful anyways.'
                }
            ]
        },

        // Dependent Clause Placement
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Dependent Clause Placement'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Most students think of the writing section as the giveaway section. Consequently, they tend to be a little careless and often skim through the questions to save some time for the dreadful reading section. The College Board has implemented a way to punish this behavior, as seen in this question.'
                },
                {
                    type: 'Question',
                    content: {
                        id: 4,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'The first computerized spreadsheet, Dan Bricklin’s VisiCalc, improved financial recordkeeping not only by providing users with an easy means of adjusting data in spreadsheets but also by automatically updating all calculations that were dependent on these __________ to VisiCalc’s release, changing a paper spreadsheet often required redoing the entire sheet by hand, a process that could take days.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: [
                            'adjustments prior',
                            'adjustments, prior',
                            'adjustments. Prior',
                            'adjustments and prior'
                        ],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content:
                        'If you only pay attention to the blanks, the sentence will be <i>“...calculations that were dependent on these adjustments prior to VisiCalc’s release…”</i>. (A) seems like the perfect option. However, it is actually the wrong answer.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'To approach this type of question, we must determine the type of clause before and after the blank in order to choose the right punctuation mark. Here, the 2 clauses are:'
                },
                {
                    type: 'List',
                    content: [
                        '<i>“The first computerized spreadsheet, Dan Bricklin’s VisiCalc, improved financial recordkeeping not only by providing users with an easy means of adjusting data in spreadsheets but also by automatically updating all calculations that were dependent on these adjustments.”</i>',
                        '<i>“Prior to VisiCalc’s release, changing a paper spreadsheet often required redoing the entire sheet by hand, a process that could take days.”</i>'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Both of the clauses are independent. Therefore, a period is needed. (C) is the correct answer.'
                },
                {
                    type: 'Paragraph',
                    content: 'Let’s look at another example:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 5,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'John Constable’s painting The Hay Wain depicted a serene countryside with lush green meadows and trickling ______ the essence of tranquility as the viewer’s eyes wandered across the canvas, transporting them to a place of peaceful contemplation, and basking in the artist’s skillful portrayal of nature’s beauty.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: [
                            'streams, capturing',
                            'streams capturing',
                            'streams. Capturing',
                            'streams: capturing'
                        ],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content: 'Again, let’s look at the clause before and after the blanks:'
                },
                {
                    type: 'List',
                    content: [
                        '<i>“John Constable’s painting The Hay Wain depicted a serene countryside with lush green meadows and trickling streams,”</i>',
                        "<i>“capturing the essence of tranquility as the viewer's eyes wandered across the canvas,”</i>",
                        "<i>“transporting them to a place of peaceful contemplation, basking in the artist's skillful portrayal of nature's beauty.”</i>"
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'This time, the first clause is independent while the 2nd clause is dependent (in fact, it is a list). Therefore, (B) is the right answer.'
                }
            ]
        },

        // Transition Placement
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Transition Placement'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'The dSAT might also ask you to identify what punctuation should surround a transition. Consider the following question:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 6,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'Journalists have dubbed Gil Scott-Heron the “godfather of rap”, a title that has appeared in hundreds of articles about him since the 1990s. Scott-Heron himself resisted the godfather ________ feeling that it didn’t encapsulate his devotion to the broader African American blues music tradition as well as “bluesologist,” the moniker he preferred.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: [
                            'nickname, however',
                            'nickname, however;',
                            'nickname, however,',
                            'nickname; however,'
                        ],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content:
                        'Similar to questions about placement of dependent clauses, To approach this type of question, we must determine the type of clause before and after the blank in order to choose the right punctuation mark.'
                },
                {
                    type: 'List',
                    content: [
                        'If one of the clauses are independent or not a complete thought, the transition is used non-essentially and must be surrounded by comma (<i>“, however,”</i>).',
                        'If both clauses are independent:'
                    ]
                },
                {
                    type: 'List Lv2',
                    content: [
                        'If their relationship corresponds to the transition (Additive, Adversative, Causal), the transition is used to link the 2 clauses and must be set off by a period or semicolon and end with a comma (<i>“; however,”</i>).',
                        'Otherwise, the transition is used to link the sentence before the blank and the sentence before that. Therefore, the transition must be set off by comma and end with a period or semicolon (<i>“, however;”</i>).'
                    ]
                },
                {
                    type: 'Paragraph',
                    content: 'Back to the example. Let’s look at the clauses around the blank:'
                },
                {
                    type: 'List',
                    content: [
                        '<i>“Scott-Heron himself resisted the godfather nickname.”</i>',
                        '<i>“feeling that it didn’t encapsulate his devotion to the broader African American blues music tradition as well as ‘bluesologist,’ the moniker he preferred”</i>'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Since the latter is dependent, the transition is used non-essentially in the middle of an independent clause, hence, must be surrounded by commas. (C) is the correct answer.'
                },
                {
                    type: 'Paragraph',
                    content: 'Let’s look at another example:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 7,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'To humans, it does not appear that the golden orb-weaver spider uses camouflage to capture its _________ the brightly colored arachnid seems to wait conspicuously in the center of its large circular web for insects to approach. Researcher Po Peng of the University of Melbourne has explained that the spider’s distinctive coloration may in fact be part of its appeal.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: ['prey, rather,', 'prey rather,', 'prey, rather;', 'prey; rather,'],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content: 'Again, let’s look at the clauses around the blank:'
                },
                {
                    type: 'List',
                    content: [
                        '<i>“To humans, it does not appear that the golden orb-weaver spider uses camouflage to capture its prey.”</i>',
                        '<i>“The brightly colored arachnid seems to wait conspicuously in the center of its large circular web for insects to approach.”</i>'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'These clauses are both independent, and the 2nd one gives an alternative to the hunting method used by the golden orb-weaver spider in the 1st, which is what “rather” means. Therefore, the word is at the start of the 2nd sentence and is used to join the 2 clauses. (D) is the correct answer.'
                },
                {
                    type: 'Paragraph',
                    content: 'Another one:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 8,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'During the English neoclassical period (1660-1789), many writers imitated the epic poetry and satires of ancient Greece and Rome. They were not the first in England to adopt the literary modes of classical __________ some of the most prominent figures of the earlier Renaissance period were also influenced by ancient Greek and Roman literature.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: [
                            'antiquity, however',
                            'antiquity, however,',
                            'antiquity, however;',
                            'antiquity; however,'
                        ],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content: 'You probably get the gist of it by now:'
                },
                {
                    type: 'List',
                    content: [
                        '<i>“They were not the first in England to adopt the literary modes of classical.”</i>',
                        '<i>“Some of the most prominent figures of the earlier Renaissance period were also influenced by ancient Greek and Roman literature.”</i>'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'These clauses are both independent, but they don’t have the opposite meaning of each other like what the transition “however” suggests. If we go back to the first sentence of the passage and read it, we can see that it contradicts the sentence before the blank.'
                },
                {
                    type: 'List',
                    content: [
                        '<i>“…many writers imitated the epic poetry and satires of ancient Greece and Rome.”</i>',
                        '<i>“They were not the first in England to adopt the literary modes of classical.”</i>'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'Therefore, the transition is placed at the end of the sentence before the blank. (C) is the correct answer.'
                }
            ]
        },

        // Distracting Transition
        {
            type: 'Section',
            title: {
                type: 'Heading 2',
                content: 'Distracting Transition'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'Another trap in the test is the use of transition in the answer choices to distract test takers from the right answer. Remember that a transition is non-essential: the passage is better with it, but it is grammatically acceptable without it. Here is an example:'
                },
                {
                    type: 'Question',
                    content: {
                        id: 9,
                        title: 'PUNCTUATIONS: EXAMPLE',
                        intro: null,
                        passage:
                            'Over twenty years ago, in a landmark experiment in the psychology of choice, professor Sheena Iyengar set up a jam-tasting booth at a grocery store. The number of jams available for tasting _________ some shoppers had twenty-four different options, others only six. Interestingly, the shoppers with fewer jams to choose from purchased more jam.',
                        passage2: null,
                        graph: null,
                        question:
                            'Which choice completes the text so that it conforms to the conventions of Standard English?',
                        choices: ['varied:', 'varied,', 'varied, while', 'varied while'],
                        
                    }
                },
                {
                    type: 'Paragraph',
                    content:
                        'You might be tempted to consider between (C) and (D) because the transition <i>“while”</i> correctly expresses the relationship between the 2 independent clauses after the blank:'
                },
                {
                    type: 'List',
                    content: [
                        '<i>“some shoppers had twenty-four different options”</i>',
                        '<i>“others only six”</i>'
                    ]
                },
                {
                    type: 'Paragraph',
                    content:
                        'The problem is that the clauses before and after the blank are both independent, but none of the choices that have <i>“while”</i> uses the right punctuation mark to separate 2 independent clauses. Hence we can eliminate (C) and (D) (and even (B) because you cannot use a comma to join 2 independent clauses). (A) is the correct answer.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'To double-check, notice that the clauses after the blank (<i>“some shoppers had twenty-four different options, others only six”</i>) is an explanation for the one before (<i>“The number of jams available for tasting varied”</i>). A colon, as used in (A), correctly expresses this relationship.'
                }
            ]
        },

        // Summary
        {
            type: 'Section',
            title: {
                type: 'Heading 1',
                content: 'Summary'
            },
            content: [
                {
                    type: 'Paragraph',
                    content:
                        'In this lecture, we have familiarized ourselves with a lot of grammar concepts. We have covered conjunctions (remember that we have <b>FANBOYS</b> - you should be able to list them by now - and subordinating conjunctions, which are not FANBOYS). We learned how to differentiate independent clauses from dependent ones and why it is important to tell the difference.'
                },
                {
                    type: 'Paragraph',
                    content:
                        'After you have been taken through the grammatical rules of the 6 most common punctuations that the SAT tests, we have discussed various methods to avoid common traps set by the examiners.'
                }
            ]
        }
    ],
    answers: [1, 2, 2, 2, 2, 1, 2, 3, 2, 0]
}