module.exports = {
	root: true,
	extends: [
		'eslint:recommended',
		'plugin:@javascript-eslint/recommended',
		'plugin:svelte/recommended',
		'prettier'
	],
	parser: '@javascript-eslint/parser',
	plugins: ['@javascript-eslint'],
	parserOptions: {
		sourceType: 'module',
		ecmaVersion: 2020,
		extraFileExtensions: ['.svelte']
	},
	env: {
		browser: true,
		es2017: true,
		node: true
	},
	overrides: [
		{
			files: ['*.svelte'],
			parser: 'svelte-eslint-parser',
			parserOptions: {
				parser: '@javascript-eslint/parser'
			}
		}
	]
};
