import { client } from '$lib/server/contentful.ts';
import { error } from '@sveltejs/kit';
import { adminDB, adminAuth } from '$lib/server/admin.ts';

export async function load({ params, locals }) {
    // Fetch the result from the database 
    let result = await adminDB.collection("Simulation").doc(params.slug).get();

    if (!result.exists) error(404);

    // Extract the data
    result = result.data();

    delete result.submitTime;

    // Fetch the simulation from Contentful
    let simulation = await client.getEntries({ 
        "fields.slug": result.simulation,
        content_type: 'simulation',
        include: 2
    }).catch((e) => error(404, e.message));

    if (!simulation) error(404);

    // Extract the data
    simulation = simulation.items[0].fields;    

    // Extract the data
    simulation.modules = simulation.modules.map(module => module.fields)
    simulation.modules.forEach(module => {
        module.questions = module.questions.map(question => {
            question = question.fields;
            question.correctAnswer = question.correctAnswer.correctAnswer;

            return question;
        });
    })    

    // Calculate the score
    const questions = simulation.modules.map(module => module.questions).flat();  

    let scoreOfTypes = [
        {
            questionType: "Transitions",
            CBType: "Expression of Ideas",
            subtype: ["Transitions"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Student's Notes",
            CBType: "Expression of Ideas",
            subtype: ["Student's Notes"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Punctuations",
            CBType: "Standard English Conventions",
            subtype: ["Punctuations"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Grammar",
            CBType: "Standard English Conventions",
            subtype: ["Form, Structure & Sense"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Words in Context",
            CBType: "Expression of Ideas",
            subtype: ["Fill in the Blank", "Meaning in Context"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Reading Comprehension",
            CBType: "Information and Ideas",
            subtype: ["Specific Detail", "Primary Purpose", "Main Idea", "Overall Structure"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Critical Reading",
            CBType: "Information and Ideas",
            subtype: ["Sentence Completion", "Illustrate/Support/Undermine"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Synthesis Reading",
            CBType: "Information and Ideas",
            subtype: ["Paired Passage", "Graph & Chart"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Algebra",
            CBType: "Algebra & Advanced Math",
            subtype: ["Algebra"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Data Analysis",
            CBType: "Problem-Solving & Data Analysis",
            subtype: ["Data Analysis"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Geometry",
            CBType: "Geometry & Trigonometry",
            subtype: ["Geometry"],
            score: 0,
            count: 0,
        },
    ]
        
    // Loop through the question
    for (let i = 0; i < questions.length; i++) {
        const currentQuestion = questions[i];
        const correctAnswer = currentQuestion.correctAnswer;
        const res = result.answers[i];
        const type = currentQuestion.questionType;

        // Extremely Spaghetti, but it checks for correct answer
        const isCorrect = (typeof correctAnswer !== 'object' && correctAnswer === res) ||
            typeof correctAnswer === 'object' && res && correctAnswer.recommended.concat(correctAnswer.possible).some(
                answer => res == answer || ( res?.includes('/') && (res.split('/')[0] / res.split('/')[1]) == answer
        ))

        // Add scores and counts to types
        scoreOfTypes.forEach( category => {
            if (category.subtype.includes(type)) {
                if (isCorrect) category.score++;
                category.count++;
            };
        })
    }

    // Get the student's name
    let owner = await adminAuth.getUser(result.user);
    owner = await owner.toJSON();
    const name = owner.displayName;

    return {
        name,
        title: "Simulation " + (1 + parseInt(result.simulation)),
        student: result,
        scoreOfTypes,
    };
}
