<script lang="ts">
    import ProgressBar from "./ProgressBar.svelte";

    interface Props {
        skills: { sectionTitle: string, isVerbal: boolean, sections: { score: number, count: number, questionType: string }[]}[];
    }

    let { skills }: Props = $props();
</script>
<div class="container">
    {#each skills as { sections, sectionTitle, isVerbal }}
    <div class="section-container">
        <p class="section">{sectionTitle}</p>
        <div class="bars" style="--light-color: var({isVerbal ? "--light-sky-blue" : "--light-rose"}); --color: var({isVerbal ? "--sky-blue" : "--rose"})">
            {#each sections as { score, count, questionType}}
                <ProgressBar percentage={score * 100 / count} --height="0.5rem" />
                <p class="question-type">{questionType}</p>
            {/each}
        </div>
    </div>
    {/each}
</div>


<style>
    .container {
        display: inline-flex;
        width: 100%;
        gap: 1.5rem;
        justify-content: space-between;
        align-items: start;

        padding: 2.5rem 1.6875rem;
        border: 1px solid var(--pitch-black);
        border-radius: 0.5rem;
        background-color: var(--light-purple);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .section-container {
        display: flex;
        flex-direction: column;
        gap: 1.3125rem;
        flex-basis: 100%;
        flex-grow: 1;
    }

    .section {
        font-size: 1.125rem;
        font-family: "Inter";
        font-weight: 600;
    }

    .bars {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .question-type {
        font-size: 0.875rem;
        font-family: "Inter";
        font-weight: 500;
    }

    @media (max-width: 540px) {
        .container {
            flex-direction: column;
            gap: 1.5rem;
            padding: 1rem;
        }

        .section-container {
            width: 100%;
        }
    }
</style>