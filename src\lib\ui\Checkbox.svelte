<script>
	import P2 from "./typography/P2.svelte";


    /** @type {{label?: string, isChecked?: boolean}} */
    let { label = "", isChecked = $bindable(false) } = $props();
    
    function toggleCheckbox() {
        isChecked = !isChecked;
    } 
</script>

<button class="wrapper" onclick={toggleCheckbox}>
    {#if isChecked}
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.5" y="1" width="23" height="23" rx="3.5" fill="#66E2FF"/>
            <rect x="0.5" y="1" width="23" height="23" rx="3.5" stroke="black"/>
            <path d="M18 8L9.75 16.25L6 12.5" stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    {:else}
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.5" y="1" width="23" height="23" rx="3.5" fill="white"/>
            <rect x="0.5" y="1" width="23" height="23" rx="3.5" stroke="#D2D5DA"/>
        </svg>
        
    {/if}
    
    <P2>{label}</P2>
</button>

<style>
    .wrapper {
        display: inline-flex;
        gap: 0.5rem;
        align-items: center;
        width: 100%;
    }
</style>