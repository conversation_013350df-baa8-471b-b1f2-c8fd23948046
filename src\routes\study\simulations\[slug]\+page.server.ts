import { error } from '@sveltejs/kit';
import { client } from '$lib/server/contentful.ts';

export async function load({ params, locals }) {

    // Fetch the practice arena
    let simulations: any = await client.getEntries({        
        "fields.slug": params.slug,
        content_type: 'simulation',
        include: 2
    }).catch((e) => error(404, e.message));

    if (simulations.total == 0) error(404);
    
    // Extract the data
    simulations = simulations.items[0].fields;
    
    simulations.modules = simulations.modules.map(module => module.fields)
    simulations.modules.forEach(module => {
        module.title = (module.title.at(-3) == "V" ? "R&W" : "Math") + " - Module " + module.title.at(-1);
        module.questions = module.questions.map(question => {
            question = question.fields;

            question.graph = question.graph?.fields.file.url;

            question.passage = question.passage?.passage;
            question.correctAnswer = question.correctAnswer.correctAnswer;
    
            if (question.choiceA) {
                question.choices = [
                    question.choiceA,
                    question.choiceB,
                    question.choiceC,
                    question.choiceD
                ]
            } else {
                question.choices = null;
            }
    
            delete question.choiceA
            delete question.choiceB
            delete question.choiceC
            delete question.choiceD
            delete question.explanation;

            return question;
        });
    })    

    simulations.modules.splice(2, 0, { title: 'Breaking', simulation: '', module: '', questions: [] })

    return {
        ...simulations,
        uid: locals.uid,
        slug: params.slug
    }    
}
