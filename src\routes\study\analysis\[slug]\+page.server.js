import { client } from '$lib/server/contentful.ts';
import { error } from '@sveltejs/kit';
import { adminDB } from '$lib/server/admin.ts';
import { documentToHtmlString } from '@contentful/rich-text-html-renderer';
import { BLOCKS } from '@contentful/rich-text-types';

export async function load({ params, locals }) {
    // Fetch the result from the database 
    let result = await adminDB.collection("Simulation").doc(params.slug).get();

    if (!result.exists) error(404);

    // Extract the data
    result = result.data();

    if (result.user !== locals?.uid && locals?.uid !== "ZUIN6faaaZWVDCLufiG80uJZPwH3") error(403);

    delete result.submitTime;

    // Fetch the simulation from Contentful
    let simulation = await client.getEntries({ 
        "fields.slug": result.simulation,
        content_type: 'simulation',
        include: 2
    }).catch((e) => error(404, e.message));

    if (!simulation) error(404);

    // Extract the data
    simulation = simulation.items[0].fields;    

    // Extract the data
    simulation.modules = simulation.modules.map(module => module.fields)
    simulation.modules.forEach(module => {
        module.title = (module.title.at(-3) == "V" ? "R&W" : "Math") + " - Module " + module.title.at(-1);
        module.questions = module.questions.map(question => {
            question = question.fields;

            question.graph = question.graph?.fields.file.url;
            question.passage = question.passage?.passage;
            question.correctAnswer = question.correctAnswer.correctAnswer;

            const options = {
                renderNode: {
                [BLOCKS.UL_LIST]: (node, next) => `<ul style="padding-left: 32px; display: flex; flex-direction: column; gap: 4px;">${next(node.content)}</ul>`,
                [BLOCKS.EMBEDDED_ASSET]: ({ data: { target: { fields }}}) =>
                    `<div style="display: flex; justify-content: center; width: 100%"><img src="${fields.file.url}" width="70%" alt="${fields.title}"/></div>`,
                }
            }

            question.explanation = documentToHtmlString(question.explanation, options);

            if (question.choiceA) {
                question.choices = [
                    question.choiceA,
                    question.choiceB,
                    question.choiceC,
                    question.choiceD
                ]
            } else {
                question.choices = null;
            }

            delete question.choiceA;
            delete question.choiceB;
            delete question.choiceC;
            delete question.choiceD;

            return question;
        });
    })    

    // Calculate the score
    const questions = simulation.modules.map(module => module.questions).flat();

    const TIPS = {
        "Student's Notes": [
            "These questions might be intimidatingly long, but you just need to know the right strategy to handle them.",
            "These questions might be intimidatingly long, but you just need to know the right strategy to handle them.",
            "Try to focus on the keywords to work out the correct answer more quickly."
        ],
        Transitions: [
            "To improve, revise foundational knowledge about types of relationship between clauses.",
            "You have a good base, but transitions are easy to learn, hard to master. Continue practicing!",
            "Take a look at our tips to avoid falling into traps set up by the test makers."
        ],
        Punctuations: [
            "Try to become familiar with concepts of clauses and basic punctuation rules.",
            "Your English is good. Try to learn basic punctuation rule systematically.",
            "Look for common motifs that the test makers use to avoid falling into traps."
        ],
        Grammar: [
            "Revise the basic convention of English grammar.",
            "Consume more English contents (articles, books, videos, podcasts,...) to improve your fluency.",
            "Refine your knowledge to avoid falling into traps."
        ],
        "Words in Context": [
            "Try to build your vocab base to comprehend the context of the passage.",
            "Learn to infer the meaning of an unfamiliar word from contextual clues.",
            "Learn to identify the meaning of a new word from its origin or from its second meaning."
        ],
        "Reading Comprehension": [
            "Read the question caresully to identify the information you need to look for.",
            "Learn to scan the topic and track the ideas of the passage quickly",
            "Manage time better with lexical inferencing (understanding a text by reading only 50-70%)."
        ],
        "Critical Reading": [
            "Learn to summarize a long text and extract key information.",
            "Learn to make an inference and train your critical and logical thinking.",
            "Use process of elimination to avoid falling into the test makers' traps."
        ],
        "Synthesis Reading": [
            "Learn to manage two sources of information at a time.",
            "Learn to make an educated guess on the information you need to look for in the correct answer.",
            "Use one source of information at a time to rule our incorrect answer choices."
        ],
        Algebra: [
            "Revise basic concepts of equations, functions and graphs.",
            "Learn to use DESMOS to assist with your calculation and visualization (for graphs).",
            "Analyze your mistakes and read the explanation carefully to learn from experience."
        ],
        "Data Analysis": [
            "Learn the basics of probability, statistics and data modeling in English if it's not your mother tongue.",
            "Read the question (at the end) carefully to avoid traps, especially for word problems.",
            "For lengthy questions, learn to track the infomation as you speed read to not get confused."
        ],
        Geometry: [
            "Review basic shapes, properties and formulas.",
            "Get creative and try to apply, even combine formulas in various way to solve a problem.",
            "Double check your answers and remember to read the questions carefully"
        ],

    };    

    let scoreOfTypes = [
        {
            questionType: "Transitions",
            CBType: "Expression of Ideas",
            subtype: ["Transitions"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Student's Notes",
            CBType: "Expression of Ideas",
            subtype: ["Student's Notes"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Punctuations",
            CBType: "Standard English Conventions",
            subtype: ["Punctuations"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Grammar",
            CBType: "Standard English Conventions",
            subtype: ["Form, Structure & Sense"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Words in Context",
            CBType: "Expression of Ideas",
            subtype: ["Fill in the Blank", "Meaning in Context"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Reading Comprehension",
            CBType: "Information and Ideas",
            subtype: ["Specific Detail", "Primary Purpose", "Main Idea", "Overall Structure"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Critical Reading",
            CBType: "Information and Ideas",
            subtype: ["Sentence Completion", "Illustrate/Support/Undermine"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Synthesis Reading",
            CBType: "Information and Ideas",
            subtype: ["Paired Passage", "Graph & Chart"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Algebra",
            CBType: "Algebra & Advanced Math",
            subtype: ["Algebra"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Data Analysis",
            CBType: "Problem-Solving & Data Analysis",
            subtype: ["Data Analysis"],
            score: 0,
            count: 0,
        },
        {
            questionType: "Geometry",
            CBType: "Geometry & Trigonometry",
            subtype: ["Geometry"],
            score: 0,
            count: 0,
        },
    ]
    
    let scoreOfTopics = [
        {
            topic: "Natural Science",
            score: 0,
            count: 0
        },
        {
            topic: "Social Science",
            score: 0,
            count: 0
        },
        {
            topic: "Humanities",
            score: 0,
            count: 0
        },
        {
            topic: "Fiction",
            score: 0,
            count: 0
        },
    ]
    
    // Loop through the question
    for (let i = 0; i < questions.length; i++) {
        const currentQuestion = questions[i];
        const correctAnswer = currentQuestion.correctAnswer;
        const res = result.answers[i];
        const topic = currentQuestion.topic;
        const type = currentQuestion.questionType;

        // Extremely Spaghetti, but it checks for correct answer
        const isCorrect = (typeof correctAnswer !== 'object' && correctAnswer === res) ||
            typeof correctAnswer === 'object' && res && correctAnswer.recommended.concat(correctAnswer.possible).some(
                answer => res == answer || ( res?.includes('/') && (res.split('/')[0] / res.split('/')[1]) == answer
        ))

        // Add scores and counts to topics
        if (["Natural Science", "Social Science", "Humanities", "Fiction"].includes(topic)) {
            scoreOfTopics.forEach( category => {
                if (category.topic == topic) {
                    if (isCorrect) category.score++;
                    category.count++;
                }
            })
        }

        // Add scores and counts to types
        scoreOfTypes.forEach( category => {
            if (category.subtype.includes(type)) {
                if (isCorrect) category.score++;
                category.count++;
            };
        })
    }

    // Assign tips to each category
    scoreOfTypes.forEach( category => {
        const score = category.score / category.count;

        if (score == 1) {
            category.tips = "You're doing great! Keep up the good work!";
            return;
        }

        const index = score <= 0.5 ? 0 : score <= 0.8 ? 1 : 2;
        category.tips = TIPS[category.questionType][index];
    })    

    // Prioritize the weaknesses
    const priorityOrder = [
        // First block
        'Student\'s Notes 40%',
        'Student\'s Notes 70%',
        'Punctuations 40%',
        'Grammar 40%',
        'Transitions 40%',
        'Words in Context 40%',
        'Reading Comprehension 40%',
        'Student\'s Notes 100%',
        'Punctuations 70%',
        'Grammar 70%',
        'Transitions 70%',
        'Critical Reading 40%',
        
        // Second block
        'Words in Context 70%',
        'Reading Comprehension 70%',
        'Punctuations 100%',
        'Grammar 100%',
        'Transitions 100%',
        'Critical Reading 70%',
        'Synthesis Reading 40%',
        'Synthesis Reading 70%',
        
        // Third block
        'Words in Context 100%',
        'Reading Comprehension 100%',
        'Critical Reading 100%',
        'Synthesis Reading 100%'
    ];

    // Determine the verbal weaknesses
    let verbalWeaknesses = structuredClone(scoreOfTypes).slice(0, -3).filter(x => x.score != x.count);
    verbalWeaknesses.sort((a, b) => {
        const percentageA = (a.score / a.count);
        const percentageB = (b.score / b.count);

        let percentageInStringA;
        let percentageInStringB;

        if (percentageA <= 0.4) percentageInStringA = "40%"
        else if (percentageA <= 0.7) percentageInStringA = "70%"
        else percentageInStringA = "100%";

        if (percentageB <= 0.4) percentageInStringB = "40%"
        else if (percentageB <= 0.7) percentageInStringB = "70%"
        else percentageInStringB = "100%";

        const stringA = a.questionType + " " + percentageInStringA;
        const stringB = b.questionType + " " + percentageInStringB;

        return priorityOrder.indexOf(stringA) - priorityOrder.indexOf(stringB);
    })

    // Determine the math weakness, if needed
    let mathWeakness;
    if (result.predictedMathScore < 750) {
        mathWeakness = scoreOfTypes.slice(-3).sort((a, b) => a.score / a.count - b.score / b.count)[0];
    }


    let weaknesses = [];

    // Finalize the weaknesses and strengths based on the predicted total score and math score
    if (mathWeakness && result.predictedTotalScore > 1550) {
        weaknesses = [verbalWeaknesses[0], mathWeakness];
    } else if (!mathWeakness && result.predictedTotalScore > 1550) {
        weaknesses = verbalWeaknesses.slice(0, 2);
    } else if (mathWeakness && result.predictedTotalScore <= 1550) {
        weaknesses = verbalWeaknesses.slice(0, 3).concat([mathWeakness]);
    } else {
        weaknesses = verbalWeaknesses.slice(0, 4);
    }

    // Determine the strengths
    let strengths = scoreOfTypes.filter(x => !weaknesses.includes(x)).sort((a, b) => b.score / b.count - a.score / a.count).slice(0, 4);

    // Add the reviews for the strengths and 
    const WEAKNESS_REVIEWS = {
        "Student's Notes": "These questions are dreadfully long, requiring you to comprehend and select from a lot of information, which might easily lead to fatigue.",
        "Transitions": "The relationship between sentences or arguments can be unclear or confusing, especially when there are 2 or more similar answer choices.",
        "Punctuations": "In academic written English, you need to pay more attention to punctuations rules compared to conversational English. If you do not have a systematic method to learn them, they can overwhelmingly you easily.",
        "Grammar": "Academic written English tends to have stricter rules than conversational English. In addition, you might not be used to English grammar if it is not your mother tongue.",
        "Words in Context": "You found these questions challenging probably because you are more used to popular English contents than academic and scientific pieces. As a result, you lack the ability to guess the meaning of an unfamiliar word based on the words surrounding it.",
        "Reading Comprehension": "Although they do not require any critical thinking, the passages in these questions are usually long and the information is spread throughout sentences, making it harder to scan for the correct answer.",
        "Critical Reading": "These questions are intimidating as the passages are long and full of noise information, which frustrates your ability in making logical inferences to come up with the right answer.",
        "Synthesis Reading": "These are characterized by two sources of information instead of one. Reading, understanding and processing all the information can be really time consuming, especially if you do not yet have a systematic method to deal with these.",
        "Algebra": "Although most of algebra questions are quite fundamental, there are still some that test uncommon formulas. If you are not used to them, it is quite difficult to come up with them on the spot of the exam. On the other hand, easier questions tend to have traps, causing you to lose your precious points.",
        "Data Analysis": "These questions are often long and contain English terminologies that you might not be familiar with. Besides, there are word problems with a lot of details, demanding you to be able to select only the necessary information to save time.",
        "Geometry": "In Geometry, there are a lot of 2D and 3D shapes, each shape has a lot of properties. They can appear burdensome as there are too many formulas and their application varies with each question."
    };

    const STRENGTH_REVIEWS = {
        "Student's Notes": "You quickly locate key information by focusing on relevant keywords.",
        "Transitions": "You understand how each argument relates and know how to use transitions to clarify those links.",
        "Punctuations": "You demonstrate clear understanding of standard English conventions.",
        "Grammar": "You show familiarity with the English language and have a solid understanding of English grammar rules.",
        "Words in Context": "You can infer word meanings from surrounding sentences effortlessly.",
        "Reading Comprehension": "You are good at scanning for essential information in the question.",
        "Critical Reading": "You excel at analyzing and interpreting complex texts to make logical inferences.",
        "Synthesis Reading": "You keep track of key points from two sources of information with ease.",
        "Algebra": "You are quick with numbers and calculations.",
        "Data Analysis": "You are adept at keeping track of important information to derive the answer from there.",
        "Geometry": "You have skills in visualizing and dealing with 2D and 3D shapes."
    };


    weaknesses = weaknesses.map(weakness => {
        return {
            questionType: weakness.questionType,
            review: WEAKNESS_REVIEWS[weakness.questionType],
            percentage: (weakness.score / weakness.count)
        }
    })

    strengths = strengths.map(strength => {
        return {
            questionType: strength.questionType,
            review: STRENGTH_REVIEWS[strength.questionType],
            percentage: (strength.score / strength.count)
        }
    })

    const studyPlan = weaknesses.map(async (weakness) => {
        const threshold = ["Algebra", "Data Analysis", "Geometry"].includes(weakness.questionType) ? 0.65 : 0.5;
        const type = weakness.percentage <= threshold ? "Lower" : "Upper";

        let targetPlan = await client.getEntries({
            content_type: 'targetPlan',
            "fields.questionType": weakness.questionType,
            "fields.studentsPerformance": type,
            include: 2
        });


        targetPlan = targetPlan.items[0].fields;
        

        targetPlan.steps = targetPlan.steps.map(step => {
            const res = step.fields;
            if (res.cards) {
                res.cards = res.cards.map(card => card.fields);
            } else {
                res.cards = [];
            }            

            return res;
        });
        
        return targetPlan;
    })

    // const studyPlan = Object.values(LOWER_TARGETS).slice(0, 4);    

    return {
        practiceArena: simulation,
        student: result,
        scoreOfTypes,
        scoreOfTopics,
        weaknesses,
        strengths,
        studyPlan: await Promise.all(studyPlan)
    };
}
