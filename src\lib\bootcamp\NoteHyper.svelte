<script>
    import <PERSON><PERSON><PERSON> from './NoteHyper.svelte';
    /** @type {{hyperlink: any, i?: number}} */
    let { hyperlink, i = 0 } = $props();
</script>

{#each hyperlink as link}
    {#if typeof link == 'object'}
        <NoteHyper hyperlink={link} i={i+1}/>
    {:else}
        <a class="hyperlink" style={`margin-left: ${20 * i}px`}>{link}</a>
    {/if}
{/each}

<style>
    .hyperlink {
        color: var(--web-color-rose, #FF66C4);
        font-family: "Open Sans";
        font-size: 18px;
        font-style: normal;
        font-weight: 450;
        line-height: 27px; /* 150% */
    }
</style>