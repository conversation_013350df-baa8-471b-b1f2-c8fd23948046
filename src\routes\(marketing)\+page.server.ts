import type { PageServerLoad } from './$types';
import { client } from "$lib/server/contentful.ts"

export const load = (async () => {
    
    let blogs = await client.getEntry("4qAEDMqRm8IYVPKJpxKbnz");
    blogs = blogs.fields.blogs.map((blog) => {
        const fields = blog.fields;
        fields.coverImage = fields.coverImage.fields.file.url;
        return fields;
    });    

    return { blogs };    
}) satisfies PageServerLoad;