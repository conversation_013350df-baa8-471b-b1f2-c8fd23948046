<script lang="ts">
	import { But<PERSON>, H1, P1 } from '$lib/ui';
	import SectionWrapper from '../SectionWrapper.svelte';
</script>

<svelte:head>
	<link fetchpriority="high" rel="preconnect" href="https://player.vimeo.com/video/1042823414?autopause=0&amp;autoplay=1&amp;muted=1&amp;loop=1&amp;player_id=0&amp;app_id=58479">
	<script src="https://player.vimeo.com/api/player.js"></script>
</svelte:head>


<SectionWrapper --padding-top="12rem" --padding-bottom="6rem" --bg-color="var(--light-yellow)">
	<div class="hero-wrapper">
		<div class="title-wrapper">
			<H1>DSAT<span class="hero-title">16</span> Simulation</H1>
		</div>
		<div class="hero-subtitle" role="complementary">
			<P1>Digital SAT practice tests that ACTUALLY improve your score</P1>
		</div>
		<div class="form-wrapper">
			<a href="#pricing">
				<Button fullWidth={true}>Get Started</Button>
			</a>
		</div>
	</div>
	<div id="demo" class="video-wrapper" style="position:relative;">
		<iframe
			src="https://player.vimeo.com/video/1042823414?autopause=0&amp;autoplay=1&amp;muted=1&amp;loop=1&amp;player_id=0&amp;app_id=58479"
			loading="eager" 
			frameborder="0"
			allow="autoplay; fullscreen"
			style="position:absolute;top:0;left:0;width:100%;height:100%;"
			class="demo-video"
			title="DSAT16 Simulation Demo"
		></iframe>
	</div>
</SectionWrapper>

<style>
	.hero-wrapper {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		text-align: center;
	}

	.title-wrapper {
		margin-bottom: 1.5625rem;
	}

	.hero-title {
		background: linear-gradient(90deg, var(--sky-blue), #f075c0);
		background-clip: text;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.hero-subtitle {
		border-radius: 4px;
		border: 1px solid #000;
		background: var(--light-rose, #fbdef0);
		box-shadow: 4px 4px 0 0 #000;
		padding: 0.5rem 1rem;
		width: fit-content;
		max-width: 610px;
		text-wrap: pretty;
	}

	.form-wrapper {
		margin-top: 2rem;
		display: flex;
		justify-content: center;
		margin-bottom: 1.5rem;
	}

	.video-wrapper {
		aspect-ratio: 16 / 9;
		width: 90%;

		margin-top: 2rem;
		border-radius: 2rem;
		box-shadow: 1.25rem 1.25rem 0 0 var(--pitch-black);
		border: 3px solid var(--pitch-black);
		background-color: var(--sky-blue);
	}

	.demo-video {
		aspect-ratio: 16 / 9;
		border-radius: 2rem;
	}
</style>
