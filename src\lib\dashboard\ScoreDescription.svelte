<script>
    let { children, isPositive } = $props();
    

</script>

<div class="score-description w-fit" style:color={isPositive ? "#42FFB7" : "#FF4242"}
style:background={isPositive ? "#D6FAED" : "#FBDEF0"}>
    {#if isPositive}
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
        <path d="M12.53 8.06211C12.3893 7.92166 12.1987 7.84277 12 7.84277C11.8012 7.84277 11.6106 7.92166 11.47 8.06211L4.46995 15.0621C4.36519 15.167 4.29387 15.3006 4.26499 15.446C4.2361 15.5914 4.25095 15.7421 4.30766 15.879C4.36438 16.016 4.4604 16.1331 4.58362 16.2155C4.70683 16.2979 4.85171 16.342 4.99995 16.3421H19C19.1482 16.342 19.2931 16.2979 19.4163 16.2155C19.5395 16.1331 19.6355 16.016 19.6922 15.879C19.749 15.7421 19.7638 15.5914 19.7349 15.446C19.706 15.3006 19.6347 15.167 19.53 15.0621L12.53 8.06211Z" fill="#42FFB7"/>
    </svg>
    {:else}
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
        <path d="M11.4697 16.1225C11.6103 16.2629 11.8009 16.3418 11.9997 16.3418C12.1984 16.3418 12.3891 16.2629 12.5297 16.1225L19.5297 9.12246C19.6344 9.01757 19.7058 8.88399 19.7346 8.73859C19.7635 8.59319 19.7487 8.4425 19.692 8.30553C19.6353 8.16857 19.5392 8.05149 19.416 7.96906C19.2928 7.88664 19.1479 7.84259 18.9997 7.84246L4.99968 7.84246C4.85144 7.84259 4.70657 7.88664 4.58335 7.96906C4.46013 8.05149 4.36411 8.16857 4.30739 8.30553C4.25068 8.4425 4.23583 8.59319 4.26472 8.73859C4.2936 8.88399 4.36493 9.01757 4.46968 9.12246L11.4697 16.1225Z" fill="#FF66C4"/>
    </svg>
    {/if}
    {@render children?.()} points
</div>

<style>
    .score-description {
        display: inline-flex;
        padding: 0px 18px;
        align-items: center;
        border-radius: 26px;
        border: 1px solid #000;
        font-family: 'Inter';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        display: inline-flex;
        padding: 0px 18px;
        align-items: center;
    }
</style>

