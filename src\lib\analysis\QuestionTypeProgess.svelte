<script>
	import { P3 } from "$lib/ui";
    import ProgressBar from "./ProgressBar.svelte";


    /** @type {{isChosen?: boolean, score: any, count: any, questionType: any, isFirstChild?: boolean, isLastChild?: boolean}} */
    let {
        isChosen = false,
        score,
        count,
        questionType,
        isFirstChild = false,
        isLastChild = false
    } = $props();
    
    let percentage = $derived(Math.ceil((score / count) * 100));
</script>

<div class="container" class:chosen={isChosen} class:first={isFirstChild} class:last={isLastChild} style="--percentage: {percentage}%">

    <div class="percentage-wrapper">
        <p class="percentage"><span>{percentage}%</span> accurate</p>
    </div>

    <ProgressBar {percentage} --height="0.75rem" />
    <P3 isBold={true}>{questionType}</P3>

</div>

<style>
    .percentage-wrapper {
        translate: clamp(8%, var(--percentage), 93%);
    }

    .percentage {
        width: fit-content;
        translate: -50%;
        font-family: "Open Sans";
        font-weight: 450;
        font-size: 1rem;
    }

    .percentage span {
        color: var(--color);
        font-weight: 700;
    }

    .container {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        padding: 1.25rem 1.875rem 1.25rem 1.25rem;
        overflow: hidden;
        border-left: 1px solid var(--pitch-black);
        border-top: 1px solid var(--pitch-black);
    }

    .chosen, .container:hover {
        background-color: var(--secondary-color, var(--light-yellow));
    }

    .first {
        border-radius: 0.75rem 0 0 0;
    }

    .last {
        border-radius: 0 0 0 0.75rem;
        border-bottom: 1px solid var(--pitch-black);
    }
</style>