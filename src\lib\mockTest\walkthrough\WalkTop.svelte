<!-- 
    @component
    ## WalkTop
    The top section of the walkthrough page. Contains the title of the test and the timer.
    
    ## Props
    - `data` - an object that contains data of the questions and the student's performance.
-->

<script>
	import SharePopUp from "$lib/analysis/SharePopUp.svelte";
    import { H5 } from "$lib/ui";

   /** @type {{title: any, hasAnalysis?: boolean, switchToAnalysis: any, switchToResult: any, isInAnalysis: any, isShareAnalysis?: boolean}} */
   let {
      title,
      hasAnalysis = false,
      switchToAnalysis,
      switchToResult,
      isInAnalysis,
      isShareAnalysis = false
   } = $props();
    
    let isPopUpOpen = $state(false);

    function changePopUp() {
        isPopUpOpen = !isPopUpOpen;
    }
</script>
   
<!-- top -->
<div class="top">
    <div class="heading">
        <div class="title">{title}</div>
        <button class="directions">
            <div class="directions-text">Directions</div>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12.71 15.5397L18.36 9.87974C18.4537 9.78677 18.5281 9.67617 18.5789 9.55431C18.6296 9.43246 18.6558 9.30175 18.6558 9.16974C18.6558 9.03773 18.6296 8.90702 18.5789 8.78516C18.5281 8.6633 18.4537 8.5527 18.36 8.45974C18.1726 8.27349 17.9191 8.16895 17.655 8.16895C17.3908 8.16895 17.1373 8.27349 16.95 8.45974L11.95 13.4097L6.99996 8.45974C6.8126 8.27349 6.55915 8.16895 6.29496 8.16895C6.03078 8.16895 5.77733 8.27349 5.58996 8.45974C5.49548 8.55235 5.42031 8.6628 5.36881 8.78467C5.31731 8.90655 5.29051 9.03743 5.28996 9.16974C5.29051 9.30204 5.31731 9.43293 5.36881 9.5548C5.42031 9.67668 5.49548 9.78712 5.58996 9.87974L11.24 15.5397C11.3336 15.6412 11.4473 15.7223 11.5738 15.7777C11.7003 15.8331 11.8369 15.8617 11.975 15.8617C12.1131 15.8617 12.2497 15.8331 12.3762 15.7777C12.5027 15.7223 12.6163 15.6412 12.71 15.5397Z" fill="#B2B2B2"/>
            </svg>
        </button>
    </div>
    {#if !isShareAnalysis}
    {#if !hasAnalysis}
        <div class="clock">
            <div class="timer">00:00</div>
            <button class="hide-timer">
                <div class="hide-timer-text">Hide</div>
            </button>
        </div>
    {:else}
        <div class="toggle-switch">
            <button onclick={switchToResult} class="toggle-button toggle-button--left" class:selected={!isInAnalysis}>
                {#if !isInAnalysis}
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.7104 7.20986C18.6175 7.11613 18.5069 7.04174 18.385 6.99097C18.2632 6.9402 18.1324 6.91406 18.0004 6.91406C17.8684 6.91406 17.7377 6.9402 17.6159 6.99097C17.494 7.04174 17.3834 7.11613 17.2904 7.20986L9.84044 14.6699L6.71044 11.5299C6.61392 11.4366 6.49998 11.3633 6.37512 11.3141C6.25026 11.2649 6.11694 11.2408 5.98276 11.2431C5.84858 11.2454 5.71617 11.2741 5.59309 11.3276C5.47001 11.3811 5.35868 11.4583 5.26544 11.5549C5.1722 11.6514 5.09889 11.7653 5.04968 11.8902C5.00048 12.015 4.97635 12.1484 4.97867 12.2825C4.98099 12.4167 5.00972 12.5491 5.06321 12.6722C5.1167 12.7953 5.19392 12.9066 5.29044 12.9999L9.13044 16.8399C9.2234 16.9336 9.334 17.008 9.45586 17.0588C9.57772 17.1095 9.70843 17.1357 9.84044 17.1357C9.97245 17.1357 10.1032 17.1095 10.225 17.0588C10.3469 17.008 10.4575 16.9336 10.5504 16.8399L18.7104 8.67986C18.8119 8.58622 18.893 8.47257 18.9484 8.34607C19.0038 8.21957 19.0324 8.08296 19.0324 7.94486C19.0324 7.80676 19.0038 7.67015 18.9484 7.54365C18.893 7.41715 18.8119 7.3035 18.7104 7.20986Z" fill="black"/>
                </svg>
                {/if}
                <H5>Result</H5>
            </button>
            <button onclick={switchToAnalysis} class="toggle-button toggle-button--right" class:selected={isInAnalysis}>
                {#if isInAnalysis}
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.7104 7.20986C18.6175 7.11613 18.5069 7.04174 18.385 6.99097C18.2632 6.9402 18.1324 6.91406 18.0004 6.91406C17.8684 6.91406 17.7377 6.9402 17.6159 6.99097C17.494 7.04174 17.3834 7.11613 17.2904 7.20986L9.84044 14.6699L6.71044 11.5299C6.61392 11.4366 6.49998 11.3633 6.37512 11.3141C6.25026 11.2649 6.11694 11.2408 5.98276 11.2431C5.84858 11.2454 5.71617 11.2741 5.59309 11.3276C5.47001 11.3811 5.35868 11.4583 5.26544 11.5549C5.1722 11.6514 5.09889 11.7653 5.04968 11.8902C5.00048 12.015 4.97635 12.1484 4.97867 12.2825C4.98099 12.4167 5.00972 12.5491 5.06321 12.6722C5.1167 12.7953 5.19392 12.9066 5.29044 12.9999L9.13044 16.8399C9.2234 16.9336 9.334 17.008 9.45586 17.0588C9.57772 17.1095 9.70843 17.1357 9.84044 17.1357C9.97245 17.1357 10.1032 17.1095 10.225 17.0588C10.3469 17.008 10.4575 16.9336 10.5504 16.8399L18.7104 8.67986C18.8119 8.58622 18.893 8.47257 18.9484 8.34607C19.0038 8.21957 19.0324 8.08296 19.0324 7.94486C19.0324 7.80676 19.0038 7.67015 18.9484 7.54365C18.893 7.41715 18.8119 7.3035 18.7104 7.20986Z" fill="black"/>
                </svg>
                {/if}
                <H5>Analysis</H5>
            </button>
        </div>
    {/if}
    {/if}

    <div class="tools">
        {#if !isInAnalysis}
        <button class="more">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 7C12.3956 7 12.7822 6.8827 13.1111 6.66294C13.44 6.44318 13.6964 6.13082 13.8478 5.76537C13.9991 5.39992 14.0387 4.99778 13.9616 4.60982C13.8844 4.22186 13.6939 3.86549 13.4142 3.58579C13.1345 3.30608 12.7781 3.1156 12.3902 3.03843C12.0022 2.96126 11.6001 3.00087 11.2346 3.15224C10.8692 3.30362 10.5568 3.55996 10.3371 3.88886C10.1173 4.21776 10 4.60444 10 5C10 5.53043 10.2107 6.03914 10.5858 6.41421C10.9609 6.78929 11.4696 7 12 7ZM12 17C11.6044 17 11.2178 17.1173 10.8889 17.3371C10.56 17.5568 10.3036 17.8692 10.1522 18.2346C10.0009 18.6001 9.96126 19.0022 10.0384 19.3902C10.1156 19.7781 10.3061 20.1345 10.5858 20.4142C10.8655 20.6939 11.2219 20.8844 11.6098 20.9616C11.9978 21.0387 12.3999 20.9991 12.7654 20.8478C13.1308 20.6964 13.4432 20.44 13.6629 20.1111C13.8827 19.7822 14 19.3956 14 19C14 18.4696 13.7893 17.9609 13.4142 17.5858C13.0391 17.2107 12.5304 17 12 17ZM12 10C11.6044 10 11.2178 10.1173 10.8889 10.3371C10.56 10.5568 10.3036 10.8692 10.1522 11.2346C10.0009 11.6001 9.96126 12.0022 10.0384 12.3902C10.1156 12.7781 10.3061 13.1345 10.5858 13.4142C10.8655 13.6939 11.2219 13.8844 11.6098 13.9616C11.9978 14.0387 12.3999 13.9991 12.7654 13.8478C13.1308 13.6964 13.4432 13.44 13.6629 13.1111C13.8827 12.7822 14 12.3956 14 12C14 11.4696 13.7893 10.9609 13.4142 10.5858C13.0391 10.2107 12.5304 10 12 10Z" fill="#B2B2B2"/>
            </svg>
            <div class="more-text">More</div>
        </button>
        {:else}
        <!-- <button class="mail">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M28.5 6.5H7.5C6.30653 6.5 5.16193 6.97411 4.31802 7.81802C3.47411 8.66193 3 9.80653 3 11V26C3 27.1935 3.47411 28.3381 4.31802 29.182C5.16193 30.0259 6.30653 30.5 7.5 30.5H28.5C29.6935 30.5 30.8381 30.0259 31.682 29.182C32.5259 28.3381 33 27.1935 33 26V11C33 9.80653 32.5259 8.66193 31.682 7.81802C30.8381 6.97411 29.6935 6.5 28.5 6.5V6.5ZM27.885 9.5L19.065 18.32C18.9256 18.4606 18.7597 18.5722 18.5769 18.6483C18.3941 18.7245 18.198 18.7637 18 18.7637C17.802 18.7637 17.6059 18.7245 17.4231 18.6483C17.2403 18.5722 17.0744 18.4606 16.935 18.32L8.115 9.5H27.885ZM30 26C30 26.3978 29.842 26.7794 29.5607 27.0607C29.2794 27.342 28.8978 27.5 28.5 27.5H7.5C7.10218 27.5 6.72064 27.342 6.43934 27.0607C6.15804 26.7794 6 26.3978 6 26V11.615L14.82 20.435C15.6638 21.2777 16.8075 21.751 18 21.751C19.1925 21.751 20.3362 21.2777 21.18 20.435L30 11.615V26Z" fill="#333333"/>
            </svg>
        </button> -->

        <button class="share" onclick={changePopUp}>
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.065 11.5648L16.5 8.11484V22.4998C16.5 22.8977 16.658 23.2792 16.9393 23.5605C17.2206 23.8418 17.6022 23.9998 18 23.9998C18.3978 23.9998 18.7794 23.8418 19.0607 23.5605C19.342 23.2792 19.5 22.8977 19.5 22.4998V8.11484L22.935 11.5648C23.0744 11.7054 23.2403 11.817 23.4231 11.8932C23.6059 11.9693 23.802 12.0085 24 12.0085C24.198 12.0085 24.3941 11.9693 24.5769 11.8932C24.7597 11.817 24.9256 11.7054 25.065 11.5648C25.2056 11.4254 25.3172 11.2595 25.3933 11.0767C25.4695 10.8939 25.5087 10.6979 25.5087 10.4998C25.5087 10.3018 25.4695 10.1058 25.3933 9.92298C25.3172 9.74019 25.2056 9.57429 25.065 9.43484L19.065 3.43484C18.9223 3.29828 18.7541 3.19124 18.57 3.11984C18.2048 2.96982 17.7952 2.96982 17.43 3.11984C17.2459 3.19124 17.0777 3.29828 16.935 3.43484L10.935 9.43484C10.7951 9.5747 10.6842 9.74074 10.6085 9.92347C10.5328 10.1062 10.4939 10.3021 10.4939 10.4998C10.4939 10.6976 10.5328 10.8935 10.6085 11.0762C10.6842 11.259 10.7951 11.425 10.935 11.5648C11.0749 11.7047 11.2409 11.8156 11.4236 11.8913C11.6064 11.967 11.8022 12.006 12 12.006C12.1978 12.006 12.3936 11.967 12.5764 11.8913C12.7591 11.8156 12.9251 11.7047 13.065 11.5648ZM31.5 20.9998C31.1022 20.9998 30.7206 21.1579 30.4393 21.4392C30.158 21.7205 30 22.102 30 22.4998V28.4998C30 28.8977 29.842 29.2792 29.5607 29.5605C29.2794 29.8418 28.8978 29.9998 28.5 29.9998H7.5C7.10218 29.9998 6.72064 29.8418 6.43934 29.5605C6.15804 29.2792 6 28.8977 6 28.4998V22.4998C6 22.102 5.84196 21.7205 5.56066 21.4392C5.27936 21.1579 4.89782 20.9998 4.5 20.9998C4.10218 20.9998 3.72064 21.1579 3.43934 21.4392C3.15804 21.7205 3 22.102 3 22.4998V28.4998C3 29.6933 3.47411 30.8379 4.31802 31.6818C5.16193 32.5257 6.30653 32.9998 7.5 32.9998H28.5C29.6935 32.9998 30.8381 32.5257 31.682 31.6818C32.5259 30.8379 33 29.6933 33 28.4998V22.4998C33 22.102 32.842 21.7205 32.5607 21.4392C32.2794 21.1579 31.8978 20.9998 31.5 20.9998Z" fill="black"/>
            </svg>    
        </button>
        {/if}
    </div>
</div>

{#if isPopUpOpen}
    <SharePopUp {changePopUp} />
{/if}

<style>
    button {
        border: 0;
        background: none;
    }

    button:hover {
        opacity: 1 !important;
    }

    .top {
        width: 100%;
        height: 90px;
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 64px;
        border-bottom: 3px solid #505050;
        display: flex;
        position: relative;
    }

    .heading {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .title {
        color: #000;
        font-family: "Open Sans";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .directions {
        display: flex;
        align-items: center;
        margin-right: auto;
    }

    .directions:hover, .more:hover {
        cursor: auto;
    }

    .directions-text {
        color: #B2B2B2;
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .clock {
        display: flex;
        flex-direction: column;
        gap: 12px;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
    }

    .timer {
        color: #B2B2B2;
        text-align: center;
        font-family: "Inter";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }
    
    .hide-timer {
        display: inline-flex;
        padding: 3px 13px;
        justify-content: center;
        align-items: center;
        border-radius: 18px;
        border: 1px solid #B2B2B2;
    }

    .hide-timer-text {
        color: #B2B2B2;
        font-family: "Inter";
        font-size: 13px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .toggle-switch {
        display: inline-flex;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);

        border: 1px solid var(--pitch-black);
        border-radius: 12.5rem;
    }

    .toggle-button {
        padding: 0.75rem 1.5rem;
        display: inline-flex;
        gap: 0.625rem;
        align-items: center;
    }

    .toggle-button:hover {
        cursor: pointer;
    }

    .toggle-button--left {
        border-radius: 12.5rem 0 0 12.5rem;
        border-right: 1px solid var(--pitch-black);
    }

    .toggle-button--right {
        border-radius: 0 12.5rem 12.5rem 0;
    }

    .selected {
        background-color: var(--sky-blue);
    }

    .tools {
        display: inline-flex;
        align-items: flex-start;
        gap: 27px;
        width: 191.7px;
        justify-content: end;
    }

    .more {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .more-text {
        color: #B2B2B2;
        font-family: "Inter";
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .mail, .share {
        border: 3px solid var(--charcoal);
        border-radius: 50%;
        padding: 0.25rem;
        box-shadow: 0.25rem 0.25rem ;
    }

    .mail {
        background-color: var(--sky-blue);
    }

    .mail:active, .share:active {
        box-shadow: none;
        translate: 0.25rem 0.25rem
    }

    @media (max-width: 540px) {
        .tools {
            width: fit-content;
        }
    }

</style>