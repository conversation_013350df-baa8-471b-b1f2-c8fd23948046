<script lang="ts">
    import { H5, P3 } from ".";

    // Timeline stages data
    const stages = [
      { label: "first 10 users", price: "$1", annualPrice: "$7", status: "completed" },
      { label: "first 50 users", price: "$2", annualPrice: "$14", status: "active" },
      { label: "first 200 users", price: "$5", annualPrice: "$35", status: "upcoming" },
      { label: "after beta", price: "$10", annualPrice: "$70", status: "upcoming" }
    ];

    let { isAnnual = $bindable(true) } = $props();

    // Helper to get the line status between stages
    function getLineStatus(index: number) {
      if (index < stages.length - 1) {
        return stages[index + 1].status;
      }
      return "";
    }
</script>

<div class="toggle-switch-container" style="--color: var(--{!isAnnual ? 'rose' : 'aquamarine'})">
  <div class="toggle-switch">
    <button onclick={() => isAnnual = false} class="toggle-button toggle-button--left" class:selected={!isAnnual}>
        <H5>Monthly</H5>
    </button>
    <button onclick={() => isAnnual = true} class="toggle-button toggle-button--right" class:selected={isAnnual}>
        <H5>Annual</H5>
    </button>
  </div>
</div>

{#if isAnnual}
  <div class="discount"><P3>-40% discount</P3></div>
{/if}

<div class="pricing-timeline" style="--color: var(--{!isAnnual ? 'rose' : 'aquamarine'})" role="list" aria-label="Pricing timeline">
    {#each stages as stage, i}
        <div class="stage-dot-label">
            <div class="price {stage.status}"><H5>{ isAnnual ? stage.annualPrice : stage.price}/{isAnnual ? "yr" : "mo"}</H5></div>
            <div class="dot {stage.status}"></div>
            <div class="label {stage.status}"><P3>{stage.label}</P3></div>
        </div>
        {#if i < stages.length - 1}
            <div class="line {getLineStatus(i)}"></div>
        {/if}
    {/each}
</div>

<style>
    .pricing-timeline {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0;
        margin: 2rem 0 3rem 0;
        flex-wrap: wrap;
        width: 100%;
    }

    .stage-dot-label {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
    }

    .dot {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        margin: 0.25rem 0;
        border: none;
    }
    .price {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.1rem;
        transition: color 0.2s, opacity 0.2s;
        position: absolute;
        top: 0;
        left: 50%;
        translate: -50% -100%;
    }
    .label {
        font-size: 1rem;
        color: var(--charcoal);
        text-align: center;
        margin-top: 0.1rem;
        transition: color 0.2s, opacity 0.2s;
        position: absolute;
        bottom: 0;
        left: 50%;
        translate: -50% 100%;
        width: 4rem;
    }
    .line {
        height: 0.25rem;
        flex: 1 1 2.5rem;
        min-width: 2.5rem;
        max-width: 10rem;
        align-self: center;
        border-radius: 0.2rem;
        margin-bottom: 0.5rem;
        margin-top: 0.5rem;
        z-index: 0;
    }

    /* Completed stages */
    .dot.completed,
    .line.completed {
        background: var(--color);
        text-decoration: line-through;
        color: var(--color);
        opacity: 0.3;
    }

    .price.completed,
    .label.completed {
        --text-color: var(--color);
        text-decoration: line-through;
        color: var(--color);
        opacity: 0.3;
    }

    /* Active stage */
    .dot.active,
    .line.active {
        background: var(--color);
    }
    
    .price.active,
    .label.active {
        --text-color: var(--color);
        opacity: 1;
    }

    /* Upcoming stage */
    .dot.upcoming,
    .line.upcoming {
        background: var(--charcoal);
        opacity: 0.15;
    }

    .price.upcoming,
    .label.upcoming {
        --text-color: var(--charcoal);
        opacity: 0.25;
    }

    .toggle-switch {
        position: relative;
        display: inline-flex;
        /* margin-top: 12rem; */

        border: 1px solid var(--pitch-black);
        border-radius: 12.5rem;
    }

    .toggle-button {
        padding: 0.75rem 1.5rem;
        display: inline-flex;
        gap: 0.625rem;
        align-items: center;
    }

    .toggle-button:hover {
        cursor: pointer;
    }

    .toggle-button--left {
        border-radius: 12.5rem 0 0 12.5rem;
        border-right: 1px solid var(--pitch-black);
    }

    .toggle-button--right {
        border-radius: 0 12.5rem 12.5rem 0;
    }

    .selected {
        background-color: var(--color);
    }

    .discount {
        background-color: var(--aquamarine);
    }
</style>