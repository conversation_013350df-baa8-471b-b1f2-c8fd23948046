<script>
    /** @type {{intro?: boolean, passage: any}} */
    let { intro = false, passage } = $props();
</script>

<p class="passage {intro ? 'fiction' : ''}">
    {@html passage}
</p>

<style>
    .passage {
        max-width: 645px;   
        width: 100%;
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .fiction {
        padding: 10px 0 0 20px;
    }
</style>