<script>

    /** @type {{setBreaking: any, isInModule: any, minutes: any, seconds: any}} */
    let {
        setBreaking,
        isInModule,
        minutes,
        seconds
    } = $props();
</script>

<div class="breaking-container">
    <div class="breaking-logo">
        <svg xmlns="http://www.w3.org/2000/svg" width="119" height="24" viewBox="0 0 119 24" fill="none">
            <path d="M94.8829 0.319392V23.6806H89.2479V5.58935H89.1111L83.8867 8.78327V3.90114L89.6472 0.319392H94.8829Z" fill="url(#paint0_linear_924_1604)"/>
            <path d="M109.412 24C108.135 24 106.91 23.7947 105.739 23.384C104.568 22.9658 103.526 22.3042 102.614 21.3992C101.701 20.4867 100.983 19.2928 100.458 17.8175C99.9333 16.3346 99.6747 14.5285 99.6823 12.3992C99.6899 10.4753 99.9257 8.74905 100.39 7.22053C100.853 5.68441 101.515 4.38023 102.374 3.30798C103.241 2.23574 104.275 1.41825 105.477 0.855513C106.686 0.285171 108.036 0 109.526 0C111.161 0 112.602 0.319392 113.85 0.958175C115.104 1.58935 116.108 2.44106 116.861 3.51331C117.614 4.57795 118.059 5.76426 118.196 7.07224H112.641C112.473 6.3346 112.104 5.77566 111.534 5.39544C110.971 5.0076 110.302 4.81369 109.526 4.81369C108.097 4.81369 107.028 5.43346 106.321 6.673C105.622 7.91255 105.264 9.57034 105.249 11.6464H105.397C105.717 10.9468 106.177 10.346 106.777 9.84411C107.378 9.34221 108.066 8.95817 108.842 8.69201C109.625 8.41825 110.454 8.28137 111.329 8.28137C112.728 8.28137 113.964 8.60456 115.036 9.25095C116.108 9.89734 116.949 10.7833 117.557 11.9087C118.165 13.0266 118.466 14.308 118.458 15.7529C118.466 17.3802 118.085 18.8175 117.317 20.0646C116.549 21.3042 115.485 22.27 114.123 22.962C112.77 23.654 111.199 24 109.412 24ZM109.378 19.6654C110.07 19.6654 110.69 19.5019 111.237 19.1749C111.785 18.8479 112.215 18.403 112.526 17.8403C112.838 17.2776 112.99 16.6426 112.983 15.9354C112.99 15.2205 112.838 14.5856 112.526 14.0304C112.222 13.4753 111.796 13.0342 111.249 12.7072C110.709 12.3802 110.089 12.2167 109.39 12.2167C108.88 12.2167 108.405 12.3118 107.964 12.5019C107.523 12.692 107.139 12.9582 106.812 13.3004C106.492 13.635 106.241 14.0304 106.059 14.4867C105.876 14.9354 105.781 15.4221 105.774 15.9468C105.781 16.6388 105.941 17.2662 106.253 17.8289C106.564 18.3916 106.99 18.8403 107.53 19.1749C108.07 19.5019 108.686 19.6654 109.378 19.6654Z" fill="url(#paint1_linear_924_1604)"/>
            <path d="M8.27519 23.1451H0V0.757256H8.26426C10.5453 0.757256 12.5093 1.20545 14.1564 2.10184C15.8107 2.99094 17.086 4.27357 17.9824 5.94975C18.8788 7.61863 19.327 9.61546 19.327 11.9402C19.327 14.2723 18.8788 16.2764 17.9824 17.9526C17.0933 19.6288 15.8216 20.915 14.1673 21.8114C12.513 22.7005 10.549 23.1451 8.27519 23.1451ZM5.41112 18.532H8.06749C9.32098 18.532 10.3813 18.3206 11.2486 17.8979C12.1231 17.468 12.7826 16.772 13.2272 15.81C13.679 14.8407 13.9049 13.5508 13.9049 11.9402C13.9049 10.3297 13.679 9.04702 13.2272 8.09233C12.7753 7.13035 12.1085 6.43802 11.2267 6.01534C10.3522 5.58536 9.27361 5.37037 7.99097 5.37037H5.41112V18.532Z" fill="white"/>
            <path d="M34.5875 7.46923C34.5146 6.66758 34.1903 6.04449 33.6145 5.59994C33.0461 5.1481 32.2335 4.92218 31.1768 4.92218C30.4772 4.92218 29.8942 5.01328 29.4278 5.19547C28.9613 5.37766 28.6115 5.62909 28.3783 5.94975C28.1451 6.26312 28.0249 6.62386 28.0176 7.03197C28.003 7.3672 28.0686 7.66236 28.2144 7.91743C28.3674 8.1725 28.586 8.39842 28.8702 8.59518C29.1618 8.78466 29.5116 8.95228 29.9197 9.09804C30.3278 9.24379 30.7869 9.37132 31.2971 9.48064L33.221 9.9179C34.3287 10.1584 35.3053 10.4791 36.1507 10.8799C37.0033 11.2807 37.7175 11.758 38.2932 12.3119C38.8763 12.8658 39.3172 13.5035 39.616 14.2249C39.9148 14.9464 40.0678 15.7554 40.0751 16.6517C40.0678 18.0656 39.7107 19.279 39.0038 20.292C38.2969 21.3049 37.2803 22.0811 35.9539 22.6204C34.6348 23.1597 33.0425 23.4293 31.1768 23.4293C29.3039 23.4293 27.6714 23.1487 26.2795 22.5876C24.8875 22.0264 23.8053 21.1738 23.0328 20.0296C22.2603 18.8854 21.8631 17.4388 21.8413 15.6898H27.0228C27.0665 16.4112 27.2597 17.0125 27.6022 17.4935C27.9447 17.9745 28.4148 18.3388 29.0124 18.5866C29.6172 18.8344 30.3169 18.9583 31.1112 18.9583C31.84 18.9583 32.4594 18.8599 32.9696 18.6631C33.487 18.4664 33.8842 18.1931 34.1611 17.8433C34.4381 17.4935 34.5802 17.0926 34.5875 16.6408C34.5802 16.2181 34.449 15.8574 34.1939 15.5586C33.9388 15.2525 33.5453 14.9901 33.0133 14.7715C32.4886 14.5456 31.8181 14.3379 31.0019 14.1484L28.6625 13.6018C26.724 13.1573 25.1972 12.4394 24.0822 11.4483C22.9672 10.4499 22.4133 9.10168 22.4206 7.40364C22.4133 6.01898 22.785 4.80558 23.5356 3.76343C24.2863 2.72129 25.3248 1.90871 26.6511 1.3257C27.9775 0.742681 29.4897 0.451172 31.1877 0.451172C32.9222 0.451172 34.4271 0.746324 35.7025 1.33663C36.9851 1.91964 37.9799 2.73951 38.6868 3.79623C39.3937 4.85295 39.7544 6.07728 39.769 7.46923H34.5875Z" fill="white"/>
            <path d="M46.9592 23.1451H41.1437L48.6974 0.757256H55.9013L63.455 23.1451H57.6394L52.3813 6.39794H52.2064L46.9592 23.1451ZM46.1831 14.3343H58.339V18.4445H46.1831V14.3343Z" fill="white"/>
            <path d="M62.3372 5.15174V0.757256H81.2597V5.15174H74.4712V23.1451H69.1366V5.15174H62.3372Z" fill="white"/>
            <defs>
              <linearGradient id="paint0_linear_924_1604" x1="120.101" y1="-4.69355" x2="93.4682" y2="-8.61015" gradientUnits="userSpaceOnUse">
                <stop stop-color="#66E2FF"/>
                <stop offset="1" stop-color="#FF66C4"/>
              </linearGradient>
              <linearGradient id="paint1_linear_924_1604" x1="120.101" y1="-4.69355" x2="93.4682" y2="-8.61015" gradientUnits="userSpaceOnUse">
                <stop stop-color="#66E2FF"/>
                <stop offset="1" stop-color="#FF66C4"/>
              </linearGradient>
            </defs>
        </svg>
    </div>
    <div class="breaking">
        <div class="breaking-timer">
            <div class="breaking-timer-text">{isInModule ? "Time Remaining in Module:" : "Remaining Break Time"}</div>
            <div class="breaking-timer-time">{minutes}:{seconds}</div>
        </div>
        <div class="breaking-info">
            {#if isInModule}
            <div class="breaking-info-title">Unscheduled Break</div>
            <ul class="breaking-info-text">
                <li>If you’re testing with a laptop, don’t close it.</li>
                <li>If this module ends while you’re on break, you’ll be taken to the next module.</li>
            </ul>
            {:else}
            <div class="breaking-info-title">Simulation Break</div>
            <div class="breaking-info-text">
                <p>You can resume this practice test as soon as you’re ready to move on. On test day, you’ll wait until the clock counts down. Read below to see how breaks work on test day.</p>
            </div>
            <div class="breaking-info-title">Take a Break: Do Not Close Your Device</div>
            <div class="breaking-info-text">
                <p>After the break, a Resume Testing Now button will appear and you’ll start the next session.</p>
                <p>Follow these rules during the break:</p>
                <ol class="breaking-info-text">
                    <li>Do not disturb students who are still testing.</li>
                    <li>Do not exit the app or close your laptop.</li>
                    <li>Do not access phones, smartwatches, textbooks, notes, or the internet.</li>
                    <li>Do not eat or drink near any testing device.</li>
                    <li>Do not speak in the test room; outside the test room, do not discuss the exam with anyone.</li>
                </ol>
            </div>
            {/if}
            <button class="breaking-info-button" onclick={setBreaking}>Resume Testing</button>
        </div>
    </div>
</div>

<style>
    button {
        border: 0;
        background: none;
    }

    .breaking-container {
        width: 100vw;
        height: 100vh;
        padding: 32px 64px;
        position: absolute;
        left: 0;
        top: 0;
        background: #1E1E1E;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .breaking-logo {
        display: inline-flex;
        align-items: center;
        gap: 15px;
        position: absolute;
        left: 64px;
        bottom: 33px;
    }
/* 
    .breaking-logo > span {
        color: #FFF;
        font-family: "Inter";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    } */

    .breaking {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        gap: 240px;
    }

    .breaking-timer {
        display: flex;
        padding: 24px 40px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 8px;
        border: 1px solid #FFF;
    }

    .breaking-timer-text {
        color: #FFF;
        font-family: "Inter";
        font-size: 18px;
        font-weight: 600;
    }

    .breaking-timer-time {
        color: #FFF;
        font-family: "Inter";
        font-size: 65px;
        font-weight: 800;
    }

    .breaking-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }

    .breaking-info-title {
        color: #FFF;
        font-family: "Inter";
        font-size: 32px;
        font-weight: 600;
        letter-spacing: -0.64px;
    }

    .breaking-info-text {
        width: 466px;
        color: #FFF;
        font-family: "Inter";
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 30px; /* 150% */
        margin-left: 30px;
    }

    .breaking-info-button {
        display: flex;
        padding: 18px 25px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 35px;
        border: 1px solid #000;
        background: var(--web-color-yellow, #FFC800);
        color: #000;
        font-family: "Inter";
        font-size: 15px;
        font-weight: 600;
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>

