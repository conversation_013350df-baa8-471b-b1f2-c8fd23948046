import { adminDB, supabase } from '$lib/server';
import type { Deck } from '$lib/types/vocab.types.js';
import { error } from '@sveltejs/kit';
import { Timestamp } from 'firebase-admin/firestore';

export async function load({ locals, params }) {
	const { deckId } = params;

	const docRef = adminDB.collection('users').doc(locals.uid).collection('decks').doc(deckId);

	const doc = await docRef.get();

	if (!doc.exists) {
		error(404, 'Deck not found');
	}

    const docData = doc.data() as Deck;

	const { data: vocabData, error: vocabError } = await supabase
		.from('vocab')
		.select('*')
		.in('vocabId', docData.cards.map((card) => card.vocabId));

	if (vocabError) {
		error(500, 'Failed to fetch vocab data');
	}

	docData.cards.forEach((card) => {
		card.due = (card.due as Timestamp).toDate();
		if (card.last_review) {
			card.last_review = (card.last_review as Timestamp).toDate();
		}
	});
	docData.createdAt = (docData.createdAt as Timestamp).toDate();
	docData.updatedAt = (docData.updatedAt as Timestamp).toDate();
	docData.id = deckId;

	// Check if there are any cards due for review
	const now = new Date();
	const hasDueCards = docData.cards.some((card) => card.due <= now);

	return {
		deck: docData,
		vocabData,
		userId: locals.uid,
		hasDueCards
	};
}