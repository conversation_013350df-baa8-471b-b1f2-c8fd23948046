import type { Timestamp } from "firebase/firestore";
import type { Card as FSRS_Card, Rating, ReviewLog, State } from "ts-fsrs";

// Vocabulary reference data
export interface Vocab {
  vocabId: number;
  vocab: string;
  meaning: string;
  charge: "Positive" | "Negative" | "Neutral";
  difficulty: 1 | 2 | 3 | 4;
  hint1: string;
  hint2: string;
}

/**
 * Card with FSRS fields, compatible with Firestore
 * - 'due' is Date when created client-side, Timestamp when read from Firestore
 */
export interface Card extends Omit<FSRS_Card, 'due' | 'last_review'> {
  id: number;
  vocabId: number;
  /**
   * Date (when created client-side) or Firestore Timestamp (when read from Firestore)
   */
  due: Date | Timestamp;
  last_review?: Date | Timestamp;
}

interface DeckTrait {
  id: string;
  name: string;
  description: string;
  createdAt: Timestamp | Date;
  updatedAt: Timestamp | Date;
}

// Deck containing cards array
export interface Deck extends DeckTrait {
  cards: Card[];              // Array of card objects
}

export interface DeckWithVocab extends DeckTrait {
  cards: (Card & Vocab)[];
}



// Study session data
export interface StudySession {
  id: string;
  deckId: string;
  cardsReviewed: number;
  accuracy: number;
  duration: number;
  completedAt: Timestamp;
  reviewLogs: ReviewLog[];
}

// Deck statistics for UI display
export interface DeckStats {
    total: number;
    new: number;
    learn: number;
    review: number;
    due: number;
}

// User progress tracking
export interface UserProgress {
  userId: string;
  totalCardsStudied: number;
  totalStudyTime: number; // in minutes
  streakDays: number;
  lastStudyDate: Timestamp;
  averageAccuracy: number;
  updatedAt: Timestamp;
} 