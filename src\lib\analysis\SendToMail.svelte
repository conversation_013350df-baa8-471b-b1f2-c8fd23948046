<script lang="ts">
	import { user } from "$lib/firebase/auth.svelte";

  interface Props {
    overview: any;
  }

  let { overview }: Props = $props();
    let email: string = $state($user?.email);
    let message: string = $state("");

    async function sendEmail() {
        const response = await fetch("/api/send-report", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                email,
                content: overview.outerHTML,
            }),
        });

        if (response.ok) {
            message = "Okay, we just sent you the full report!"
        } else {
            message = "Oops, something went wrong. Please double check the email."
        }
    }
  </script>
  
  <article class="analysis-card">
    <svg class="notification-icon" width="31" height="30" viewBox="0 0 31 30" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="15.75" cy="15" r="15" fill="#66E2FF"/>
        <path d="M21.5827 8.3335H9.91602C9.25297 8.3335 8.61709 8.59689 8.14825 9.06573C7.67941 9.53457 7.41602 10.1705 7.41602 10.8335V19.1668C7.41602 19.8299 7.67941 20.4658 8.14825 20.9346C8.61709 21.4034 9.25297 21.6668 9.91602 21.6668H21.5827C22.2457 21.6668 22.8816 21.4034 23.3505 20.9346C23.8193 20.4658 24.0827 19.8299 24.0827 19.1668V10.8335C24.0827 10.1705 23.8193 9.53457 23.3505 9.06573C22.8816 8.59689 22.2457 8.3335 21.5827 8.3335ZM21.241 10.0002L16.341 14.9002C16.2635 14.9783 16.1714 15.0403 16.0698 15.0826C15.9683 15.1249 15.8594 15.1467 15.7493 15.1467C15.6393 15.1467 15.5304 15.1249 15.4289 15.0826C15.3273 15.0403 15.2352 14.9783 15.1577 14.9002L10.2577 10.0002H21.241ZM22.416 19.1668C22.416 19.3878 22.3282 19.5998 22.1719 19.7561C22.0157 19.9124 21.8037 20.0002 21.5827 20.0002H9.91602C9.695 20.0002 9.48304 19.9124 9.32676 19.7561C9.17048 19.5998 9.08268 19.3878 9.08268 19.1668V11.1752L13.9827 16.0752C14.4514 16.5433 15.0868 16.8063 15.7493 16.8063C16.4119 16.8063 17.0473 16.5433 17.516 16.0752L22.416 11.1752V19.1668Z" fill="black"/>
    </svg>
        
    <section class="content-container">
        <!-- TODO -->
        <div class="email-form">
            <h2 class="form-title">Would you like to have an email copy of the analysis?</h2>
            <div class="input-wrapper">
                <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19.25 4.5H5.25C4.45435 4.5 3.69129 4.81607 3.12868 5.37868C2.56607 5.94129 2.25 6.70435 2.25 7.5V17.5C2.25 18.2956 2.56607 19.0587 3.12868 19.6213C3.69129 20.1839 4.45435 20.5 5.25 20.5H19.25C20.0456 20.5 20.8087 20.1839 21.3713 19.6213C21.9339 19.0587 22.25 18.2956 22.25 17.5V7.5C22.25 6.70435 21.9339 5.94129 21.3713 5.37868C20.8087 4.81607 20.0456 4.5 19.25 4.5ZM18.84 6.5L12.96 12.38C12.867 12.4737 12.7564 12.5481 12.6346 12.5989C12.5127 12.6497 12.382 12.6758 12.25 12.6758C12.118 12.6758 11.9873 12.6497 11.8654 12.5989C11.7436 12.5481 11.633 12.4737 11.54 12.38L5.66 6.5H18.84ZM20.25 17.5C20.25 17.7652 20.1446 18.0196 19.9571 18.2071C19.7696 18.3946 19.5152 18.5 19.25 18.5H5.25C4.98478 18.5 4.73043 18.3946 4.54289 18.2071C4.35536 18.0196 4.25 17.7652 4.25 17.5V7.91L10.13 13.79C10.6925 14.3518 11.455 14.6674 12.25 14.6674C13.045 14.6674 13.8075 14.3518 14.37 13.79L20.25 7.91V17.5Z" fill="#777777"/>
                </svg>
                    
            <label for="emailInput" class="visually-hidden">Email address</label>
            <input 
                type="email"
                id="emailInput"
                class="email-input"
                placeholder="<EMAIL>"
                bind:value={email}
                required
                aria-label="Email address"
            />
            </div>
            <button class="submit-button" onclick={sendEmail}>Send Analysis</button>
        </div>
      {#if message}
      <p class="confirmation-message" aria-live="polite">
        {message}
      </p>
      {/if}
    </section>
  </article>
  
  <style>
    .visually-hidden {
      clip: rect(0 0 0 0);
      clip-path: inset(50%);
      height: 0.0625rem;
      overflow: hidden;
      position: absolute;
      white-space: nowrap;
      width: 0.0625rem;
    }
  
    .analysis-card {
      filter: drop-shadow(0.25rem 0.25rem 0 #000);
      display: flex;
      max-width: 40rem;
      flex-direction: column;
      font-family: "Open Sans", sans-serif;
      font-weight: 450;
    }
  
    .notification-icon {
      aspect-ratio: 1;
      object-fit: contain;
      width: 1.875rem;
      align-self: center;
      z-index: 10;
    }
  
    .content-container {
      border-radius: 0 0 0.75rem 0.75rem;
      border: 0.0625rem solid #000;
      border-top: 0.25rem solid var(--sky-blue);
      background: #daf9ff;
      margin-top: -1rem;
      width: 100%;
      padding: 2.5rem 4.375rem;
    }
  
    .email-form {
      display: flex;
      width: 100%;
      max-width: 31.25rem;
      flex-direction: column;
      align-items: center;
      margin: 0 auto;
    }
  
    .form-title {
      color: #333;
      text-align: center;
      font-size: 1.125rem;
      margin: 0;
    }
  
    .input-wrapper {
      border-radius: 0.375rem;
      background-color: #fff;
      display: flex;
      margin-top: 1rem;
      width: 100%;
      align-items: center;
      gap: 0.625rem;
      padding: 0.625rem;
      border: 0.0625rem solid #777;
    }
  
    .email-input {
      border: none;
      outline: none;
      width: 100%;
      font-size: 1rem;
      font-family: inherit;
      background: transparent;
    }
  
    .submit-button {
      width: fit-content;
      border-radius: 0.5rem;
      border: 0.0625rem solid #000;
      background: #66e2ff;
      box-shadow: 0.25rem 0.25rem 0 #000;
      margin-top: 1rem;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: transform 0.2s;
    }
  

    .submit-button:active {
        box-shadow: none;
        translate: 0.25rem 0.25rem;
    }
  
    .confirmation-message {
      color: #a7a7a7;
      text-align: center;
      font-size: 1rem;
      margin: 0.375rem 0 0;
    }
  </style>