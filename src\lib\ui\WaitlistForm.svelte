<script lang="ts">
	import <PERSON><PERSON> from "./Button.svelte";
	import P2 from "./typography/P2.svelte";

    function protectForm(e: SubmitEvent) {
        const a_password = (e.target as HTMLFormElement).querySelector<HTMLInputElement>('input[name="a_password"]').value;

        if (a_password) {
            e.preventDefault();
        }
    }
</script>

<form method="post" action="https://sendfox.com/form/3edxjw/3zq59w" on:submit={protectForm} class="sendfox-form" id="3zq59w" data-async="true" data-recaptcha="true">
    <div class="input-wrapper">
        <div class="email-icon">
            <svg width="24" height="22" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 4.5H5C4.20435 4.5 3.44129 4.81607 2.87868 5.37868C2.31607 5.94129 2 6.70435 2 7.5V17.5C2 18.2956 2.31607 19.0587 2.87868 19.6213C3.44129 20.1839 4.20435 20.5 5 20.5H19C19.7956 20.5 20.5587 20.1839 21.1213 19.6213C21.6839 19.0587 22 18.2956 22 17.5V7.5C22 6.70435 21.6839 5.94129 21.1213 5.37868C20.5587 4.81607 19.7956 4.5 19 4.5ZM18.59 6.5L12.71 12.38C12.617 12.4737 12.5064 12.5481 12.3846 12.5989C12.2627 12.6497 12.132 12.6758 12 12.6758C11.868 12.6758 11.7373 12.6497 11.6154 12.5989C11.4936 12.5481 11.383 12.4737 11.29 12.38L5.41 6.5H18.59ZM20 17.5C20 17.7652 19.8946 18.0196 19.7071 18.2071C19.5196 18.3946 19.2652 18.5 19 18.5H5C4.73478 18.5 4.48043 18.3946 4.29289 18.2071C4.10536 18.0196 4 17.7652 4 17.5V7.91L9.88 13.79C10.4425 14.3518 11.205 14.6674 12 14.6674C12.795 14.6674 13.5575 14.3518 14.12 13.79L20 7.91V17.5Z" fill="#777777"/>
            </svg>
        </div>
        <input class="email-input" type="email" id="sendfox_form_email" placeholder="Your Best Email" name="email" required />
    </div>

    <!-- GDPR Compliant -->
    <P2><label><input type="checkbox" name="gdpr" value="1" required /> I agree to receive email updates and promotions.</label></P2>
    <!-- Anti-bot -->
    <div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text" name="a_password" tabindex="-1" autocomplete="off" /></div>
    <div class="button-wrapper">
        <Button fullWidth={true} type="submit">Join the Waitlist</Button>
    </div>
</form>

<svelte:head>
    <script src="https://cdn.sendfox.com/js/form.js" charset="utf-8"></script>
</svelte:head>

{#if false}
<div class="sendfox-message"></div>
{/if}
                

<style>
    .sendfox-form {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        width: 100%;
        max-width: var(--form-max-width, initial);
    }

    .input-wrapper {
        border-radius: 0.375rem;
        width: 100%;
        background-color: white;

        display: inline-flex;
        align-items: center;
        justify-content: start;
        gap: 0.625rem;
        border: 0.0625rem solid rgb(119, 119, 119);
    }

    .email-icon {
        padding: 0.625rem 0 0.625rem 0.625rem;
    }

    .email-input {
        border: none;
        background-color: transparent;
        font-weight: 450;
        font-size: 1.125rem;
        width: 100%;
        font-family: "Open Sans", sans-serif;
        padding: 0.625rem 0.625rem 0.625rem 0;
    }

    .email-input:focus {
        outline: none;
    }

    .button-wrapper {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        width: 100%;
    }

    .sendfox-message {
        font-family: "Open Sans";
        font-size: 1rem;
        font-weight: 450;
        line-height: 1.5rem;
        color: var(--text-color, --pitch-black);
    }
</style>