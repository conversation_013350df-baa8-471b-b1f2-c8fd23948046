<script>
	import { H4, P1 } from '$lib/ui';
	import SectionWrapper from '../SectionWrapper.svelte';
</script>

<SectionWrapper
    --padding-top="8.875rem"
    --padding-bottom="7.25rem"
    --bg-color="var(--sky-blue, #8c53ff)"

>
    <div class="message-container">
        <div class="message-content">
            <div class="first-heading">
                <P1>There's an old saying that</P1>
            </div>
            <div class="second-heading">
                <P1>The quickest way to improve your SAT score is:</P1>
            </div>

            <div class="practice-message">
                <enhanced:img
                  loading="lazy"
                  src="/static/practice-desktop.webp"
                  alt="Practice message background"
                  class="background-image"
                  sizes="min(488px, 100vw)"
                />
            </div>
        </div>
        <div class="pink-part">
            <H4>❗But Bluebook only has 6 practice tests 😩</H4>
        </div>
    </div>
</SectionWrapper>


<style>
    .message-container {
        display: flex;
        width: 100%;
        max-width: 32.5rem;
        flex-direction: column;
    }

    .message-content {
        display: flex;
        width: calc(100% - 2rem);
        flex-direction: column;
        align-self: center;
    }

    .first-heading {
        border-radius: 0.25rem;
        box-shadow: 0.5rem 0.5rem;
        background: white;
        padding: 0.625rem;
        width: fit-content;
        border: 1px solid var(--pitch-black);
    }

    .second-heading {
        border-radius: 0.25rem;
        box-shadow: 0.5rem 0.5rem;
        background: var(--yellow, #ffc800);
        padding: 0.625rem;
        width: fit-content;
        border: 1px solid var(--pitch-black);
    }

    .practice-message {
        display: flex;
        flex-direction: column;
        position: relative;
        fill: var(--light-yellow, #fff1c1);
        margin-top: 1.25rem;
        color: var(--black, #000);
        text-align: center;
        overflow: hidden;
        height: fit-content;
    }

    .background-image {
        inset: 0;
        width: 100%;
        height: auto;
        object-fit: contain;
        object-position: center;
    }

    .pink-part {
        border: 1px solid var(--pitch-black);
        background-color: var(--rose);
        padding: 0.625rem;
        border-radius: 0.25rem;
        box-shadow: 0.25rem 0.25rem;
        text-align: center;
        text-wrap: pretty;
    }
</style>
