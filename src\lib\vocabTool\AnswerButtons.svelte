<script lang="ts">
  import H5 from "$lib/ui/typography/H5.svelte";

  // Props for component functionality
  let { 
    handleKnowButtonClick,
    handleDontKnowButtonClick,
    handleSubmitAnswer,
    hasUserAnswered,
    currentPhase
  }: {
    handleKnowButtonClick: () => void,
    handleDontKnowButtonClick: () => void,
    handleSubmitAnswer: (wasAnswerCorrect: boolean) => void,
    hasUserAnswered: boolean,
    currentPhase: number
  } = $props();

</script>

<div class="button-container">

  {#if currentPhase < 3 && !hasUserAnswered}
    <button class="answer-button know" onclick={handleKnowButtonClick}><H5>know</H5></button>
    <button class="answer-button dont-know" onclick={handleDontKnowButtonClick}><H5>Don't know</H5></button>
  {:else}
    {#if hasUserAnswered}
      <button class="answer-button know" onclick={() => handleSubmitAnswer(true)}><H5>Correct</H5></button>
      <button class="answer-button dont-know" onclick={() => handleSubmitAnswer(false)}><H5>Incorrect</H5></button>
    {:else}
      <button class="answer-button next" onclick={() => handleSubmitAnswer(false)}><H5>Next</H5></button>
    {/if}
  {/if}
</div>

<style>
  /* Container for both answer buttons */
  .button-container {
    display: inline-flex;
    gap: 2.5rem;
    align-self: stretch;
    margin-top: 2rem;
    width: 100%;
  }

  /* Base styles for answer buttons */
  .answer-button {
    padding: 2.25rem 0.625rem;
    min-height: 6rem;
    border-radius: 1rem;
    border: 0.125rem solid black;
    white-space: nowrap;
    cursor: pointer;
    box-shadow: 0.3125rem 0.3125rem black;
    transition: all 0.1s ease;
    width: 100%;
    outline: none;
  }

  /* Hover and focus states for buttons */
  .answer-button:hover,
  .answer-button:focus {
    transform: translateY(-0.125rem);
    box-shadow: 0.4375rem 0.4375rem black;
  }

  /* Active state for buttons */
  .answer-button:active {
    transform: translate(0.3125rem, 0.3125rem);
    box-shadow: none;
  }


  .know {
    background-color: var(--aquamarine);
  }

  .dont-know {
    background-color: var(--rose);
  }

  .next {
    background-color: var(--yellow);
  }

  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    .button-container {
      margin-top: 1.5rem;
      gap: 1rem;
    }
  }
</style>
