<script>
	import <PERSON>Wrapper from "../SectionWrapper.svelte";
	import { H2, P1Wrapper } from "$lib/ui";

    import phuc from "$lib/assets/phuc.png?enhanced&w=400";
</script>

<SectionWrapper --bg-color="var(--sky-blue)" --padding-top="7.75rem" --padding-bottom="7.75rem">
    <div class="container">
        <div class="image-container">
            <enhanced:img class="image" src={phuc} alt="Phuc" />
        </div>
        <div class="text-container">
            <H2>Hey! I’m Phuc.</H2>
            <P1Wrapper>
                <p>I’m the CEO, CTO, CFO, C-3PO, and Senior Programmer of DSAT16. (Did you know you can just make up titles?)</p>
                <p>In 2023, I self-studied to 1600 Digital SAT. Now, I'm coding all the courses above to help other students achieve the same dream.</p>
            </P1Wrapper>
        </div>
    </div>

</SectionWrapper>

<style>
    .container {
        width: 100%;
        display: flex;
        gap: 3rem;
        justify-content: center;
        align-items: center;
    }

    .image-container {
        border-radius: 0.75rem;
        background-color: var(--pitch-black);
        border: 2px solid var(--pitch-black);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        width: 100%;
        max-width: 22rem;
        aspect-ratio: 3/4;

        display: flex;        
        object-fit: contain;
    }

    .image {
        border-radius: 0.75rem;
        width: 100%;
        height: 100%;
    }

    .text-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        max-width: 22rem;
    }

    @media (max-width: 768px) {
        .container {
            flex-direction: column;
        }
    }
</style>