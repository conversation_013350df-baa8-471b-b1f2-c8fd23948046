/**
 * All possible question types for SAT questions.
 */
export type QuestionType = "Word in Context" | "Main Purpose Underlined" | "Main Idea" | "Main Purpose" | 
                  "Overall Structure" | "Specific Detail" | "Command of Evidence" | 
                  "Paired Passage" | "Inference";

/**
 * All possible topics a question can belong to.
 */
export type Topic = "Social Science" | "Natural Science" | "Humanities" | "Fiction";

/**
 * All possible difficulty levels for a question.
 */
export type Difficulty = "Easy" | "Medium" | "Hard";

/**
 * Represents a single SAT question.
 */
export interface Question {
    id: number;
    model: string;
    intro: string | null;
    passage: string;
    passage2: string | null;
	question: string;
	correctAnswer: 0 | 1 | 2 | 3;
	answerChoices: string[];
	explanation: string;
	topic: Topic;
	difficulty: Difficulty;
    questionType: QuestionType;
    groundingChunks: any;
    firebaseId: string;
}

/**
 * For storing in users/{id}/completedQuestions/dataDoc
 * Contains data about completed questions, statistics, and marked questions
 */
export interface CompletedQuestion {
    data: CompletedQuestionData[];
    stats: Stats;
    markedQuestions: number[];
    incorrectlyAnsweredQuestions: number[];
}


/**
 * Statistics for each question type and overall performance.
 * Maps question types (including "All") to their respective statistics.
 */
export type Stats = {
    [K in QuestionType]: Stat | undefined;
};

/**
 * Represents statistics for a specific question type.
 * @property total - Total number of questions attempted
 * @property correct - Number of correctly answered questions
 */
interface Stat {
    total: number;
    correct: number;
}

/**
 * Data for a single completed question.
 * @property question - The ID of the completed question
 * @property wasAnswerCorrect - Whether the user's answer was correct
 * @property studentAnswer - The index of the answer chosen by the student (0-3)
 * @property timestamp - ISO string of when the question was completed
 */
interface CompletedQuestionData {
    question: number;
    wasAnswerCorrect: boolean;
    studentAnswer: number;
    timestamp: string;
}