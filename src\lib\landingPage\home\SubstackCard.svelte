<script lang="ts">
	import { H2, P1 } from "$lib/ui";
	import P2 from "$lib/ui/typography/P2.svelte";

  interface Props {
    title: string;
    description: string;
    coverImage: string;
    href: string;
  }

  let { title, description, coverImage, href }: Props = $props();
</script>

<article class="blog-card">
    <a {href} class="blog-card-wrapper" target="_blank">
        <div class="cover-container">
            <img class="cover-image" loading="lazy" src={coverImage + "?w=480"} alt="Blog Post">
        </div>
        <div class="content-container">
            <H2>{title}</H2>
            <P1>{description}</P1>
            <div class="link-wrapper"><P2 isBold={true}>Read full post</P2></div>
        </div>
    </a>
</article>

<style>
    .blog-card {
        width: 100%;
        max-width: 28rem;
        border-radius: 1rem;
        box-shadow: 0.25rem 0.25rem 0 var(--sky-blue);
        transition: transform 0.2s;
    }

    .blog-card:hover {
        transform: scale(1.02);
    }

    .blog-card:active {
        translate: 0.25rem 0.25rem;
        box-shadow: none;
    }

    .blog-card-wrapper {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .cover-container {
        display: flex;
        width: 100%;
        flex-direction: column;
    }

    .cover-image {
        border-radius: 1rem 1rem 0 0;
        background-color: #d9d9d9;
        border: 0.125rem solid #000;
        width: 100%;
        object-fit: cover;
        aspect-ratio: 16/9;
    }

    .content-container {
        border-radius: 0 0 1rem 1rem;
        border: 0.125rem solid #000;
        background: var(--light-sky-blue);
        padding: 2rem;
        height: 100%;
        
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .link-wrapper {
        color: var(--rose);
        text-decoration: underline;
        margin-top: auto;
    }

    @media (max-width: 768px) {
        .content-container {
            padding: 1.25rem;
        }
    }
</style>