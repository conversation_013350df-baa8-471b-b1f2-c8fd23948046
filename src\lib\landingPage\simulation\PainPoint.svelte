<script>
	import { H2, H3, H4, P1 } from "$lib/ui";
	import SectionWrapper from "../SectionWrapper.svelte";
    import StarCard from "./StarCard.svelte";

    import starBackground from "$lib/assets/1600.webp?enhanced&w=696";

</script>
<SectionWrapper
    --bg-color=var(--light-rose)
    --padding-top="11rem"
    --padding-bottom="15rem"
    --overflow="hidden"
>   
    <div class="card-wrapper">
        <div class="more-wrapper">
            <StarCard --topbar-color=var(--sky-blue) --card-width="25rem">
                <div class="more-text">
                    <H2>❓ What’s more</H2>
                </div>
            </StarCard>
        </div>
    
        <div class="note-and-image-card-wrapper">
            <div class="image-card-wrapper">
                <StarCard --topbar-color=var(--rose) image={starBackground}/>
            </div>

            <div class="note">
                <H3>According to previous test takers:</H3>
                <ul class="note-list">
                    <li><P1>Your score composition is ambiguous (why do I score 750 while all bars are full❓)</P1></li>
                    <li><P1>It’s not clear what to study to increase your score</P1></li>
                </ul>
            </div>
        </div>
    
        <div class="consequence-card-wrapper">
            <StarCard --topbar-color=var(--tangerine) --card-width="33.75rem">
                <div class="consequence-card-text">
                    <H2>⚠️ The consequence is</H2>
                    <P1>You finish all the practice tests, but see</P1>
                    <H4 --text-color=var(--tangerine)>NO IMPROVEMENTTT</H4>
                </div>
            </StarCard>
        </div>
    
        
    </div>
</SectionWrapper>



<style>

    .card-wrapper {
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: start;
        width: 85%;
    }

    .note-and-image-card-wrapper {
        position: relative;
    }

    .consequence-card-wrapper {
        position: absolute;
        left: 35%;
        top: 21rem;
        z-index: 2;
    }

    .consequence-card-text {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        text-align: center;
    }

    .more-wrapper {
        position: absolute;
        top: -5rem;
        left: clamp(-8%, calc((100% - 64rem) / 5), 16%);
        z-index: 2;
    }

    .more-text {
        width: 100%;
        text-align: start;
    }

    .note {
        background-color: white;
        width: 100%;
        max-width: 22.875rem;
        height: fit-content;
        padding: 3rem 2rem;
        border: 3px solid var(--pitch-black);
        box-shadow: 1.25rem 1.25rem;
        display: flex;
        flex-direction: column;
        gap: 0.625rem;

        position: absolute;
        right: clamp(-3%, calc((68rem - 80svw) * 1.15), 50%);
        top: -3rem;
        translate: 100%;
        z-index: 2;
    }

    .note-list {
        margin-left: 1.25rem;
    }

    @media (max-width: 1024px) {
        .card-wrapper {
            width: 100%;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .consequence-card-wrapper,
        .note,
        .more-wrapper,
        .note-and-image-card-wrapper {
            position: initial;
            translate: none;
        }

        .more-wrapper {
            margin-bottom: 9rem;
        }

        .note {
            margin-bottom: 4.1875rem;
        }

        .image-card-wrapper {
            position: absolute;
            top: 33%;
            left: 50%;
            translate: -50% -50%;
        }

        .note {
            position: sticky;
            margin-left: -0.5rem;
        }

        .note-list {
            text-wrap: pretty;
        }
    }
</style>