<script lang="ts">
	import { auth } from "$lib/firebase";
	import { Button } from "$lib/ui";
    import posthog from 'posthog-js';

    let { role }: { role: string } = $props();

    let isDropdownActive = $state(false);
    let body: HTMLElement | null = $state(null);

    const navLinks = [
        { href: "https://blog.dsat16.com", text: "Blog", isExternal: true, isUnderlined: true },
        { href: "/question-bank", text: "Question Bank ✨", isExternal: false, isUnderlined: false }
    ];

    function changeDropdown() {
        isDropdownActive = !isDropdownActive;
        body.style.overflow = isDropdownActive ? 'hidden' : 'auto';
    }
</script>

{#snippet closeIcon()}
    <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.40881 11.9998L-0.********* 21.4091L2.09042 23.5L11.4997 14.0907L20.909 23.5L22.9999 21.4091L13.5906 11.9998L22.9995 2.59091L20.9086 0.5L11.4997 9.90887L2.09085 0.5L-5.71191e-05 2.59091L9.40881 11.9998Z" fill="black"/>
    </svg>
{/snippet}

{#snippet menuIcon()}
    <svg width="23" height="3" viewBox="0 0 23 3" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 0.549805H23C23 1.82006 21.9703 2.8498 20.7 2.8498H0V0.549805Z" fill="black"/>
    </svg>
{/snippet}

<svelte:body bind:this={body}/>

<nav class="navigation-bar" aria-label="Main navigation">
    <div class='logo'>
        <a href="/" aria-label="Home">
            <svg xmlns="http://www.w3.org/2000/svg" width="9.3125rem" height="1.875rem" viewBox="0 0 149 30" fill="none">
                <path d="M118.603 0.39924V29.6008H111.559V6.98669H111.388L104.857 10.9791V4.87643L112.058 0.39924H118.603Z" fill="url(#paint0_linear_36_959)"/>
                <path d="M136.764 30C135.167 30 133.637 29.7433 132.173 29.23C130.709 28.7072 129.407 27.8802 128.266 26.749C127.126 25.6084 126.227 24.116 125.571 22.2719C124.916 20.4183 124.592 18.1606 124.602 15.499C124.611 13.0941 124.906 10.9363 125.486 9.02567C126.066 7.10551 126.893 5.47529 127.967 4.13498C129.05 2.79468 130.343 1.77281 131.845 1.06939C133.357 0.356464 135.044 0 136.907 0C138.951 0 140.752 0.39924 142.311 1.19772C143.879 1.98669 145.134 3.05133 146.075 4.39163C147.016 5.72243 147.572 7.20532 147.743 8.8403H140.8C140.59 7.91825 140.129 7.21958 139.416 6.7443C138.713 6.2595 137.877 6.01711 136.907 6.01711C135.12 6.01711 133.784 6.79182 132.9 8.34125C132.026 9.89068 131.579 11.9629 131.56 14.558H131.745C132.145 13.6835 132.72 12.9325 133.471 12.3051C134.222 11.6778 135.082 11.1977 136.051 10.865C137.031 10.5228 138.067 10.3517 139.16 10.3517C140.909 10.3517 142.454 10.7557 143.794 11.5637C145.134 12.3717 146.185 13.4791 146.945 14.8859C147.705 16.2833 148.081 17.885 148.071 19.6911C148.081 21.7253 147.606 23.5219 146.646 25.0808C145.685 26.6302 144.355 27.8375 142.653 28.7025C140.961 29.5675 138.998 30 136.764 30ZM136.722 24.5817C137.587 24.5817 138.361 24.3774 139.046 23.9686C139.73 23.5599 140.267 23.0038 140.657 22.3004C141.047 21.597 141.237 20.8032 141.227 19.9192C141.237 19.0257 141.047 18.2319 140.657 17.538C140.277 16.8441 139.744 16.2928 139.06 15.884C138.385 15.4753 137.61 15.2709 136.736 15.2709C136.099 15.2709 135.505 15.3897 134.954 15.6274C134.402 15.865 133.922 16.1977 133.513 16.6255C133.114 17.0437 132.8 17.538 132.572 18.1084C132.344 18.6692 132.225 19.2776 132.216 19.9335C132.225 20.7985 132.425 21.5827 132.815 22.2861C133.204 22.9895 133.737 23.5504 134.412 23.9686C135.087 24.3774 135.857 24.5817 136.722 24.5817Z" fill="url(#paint1_linear_36_959)"/>
                <path d="M10.344 28.9316H0V0.946814H10.3303C13.1816 0.946814 15.6367 1.50706 17.6955 2.62754C19.7633 3.73892 21.3575 5.34221 22.478 7.43743C23.5985 9.52353 24.1587 12.0196 24.1587 14.9255C24.1587 17.8406 23.5985 20.3458 22.478 22.441C21.3666 24.5362 19.777 26.1441 17.7091 27.2645C15.6412 28.3759 13.1862 28.9316 10.344 28.9316ZM6.7639 23.1652H10.0844C11.6512 23.1652 12.9767 22.901 14.0607 22.3727C15.1539 21.8352 15.9783 20.9652 16.534 19.7628C17.0988 18.5512 17.3812 16.9388 17.3812 14.9255C17.3812 12.9123 17.0988 11.309 16.534 10.1157C15.9692 8.91319 15.1357 8.04777 14.0334 7.51941C12.9402 6.98195 11.592 6.71321 9.98871 6.71321H6.7639V23.1652Z" fill="#303030"/>
                <path d="M43.2343 9.33678C43.1432 8.33472 42.7378 7.55585 42.0182 7.00017C41.3076 6.43537 40.2919 6.15297 38.971 6.15297C38.0965 6.15297 37.3677 6.26684 36.7847 6.49458C36.2017 6.72232 35.7644 7.0366 35.4729 7.43743C35.1814 7.82914 35.0311 8.28007 35.022 8.79021C35.0038 9.20925 35.0857 9.57819 35.2679 9.89703C35.4592 10.2159 35.7325 10.4983 36.0878 10.7442C36.4522 10.9811 36.8895 11.1906 37.3996 11.3728C37.9097 11.555 38.4836 11.7144 39.1213 11.851L41.5263 12.3976C42.9109 12.6982 44.1316 13.0991 45.1883 13.6001C46.2542 14.1011 47.1469 14.6978 47.8666 15.3901C48.5953 16.0825 49.1465 16.8796 49.52 17.7814C49.8935 18.6833 50.0848 19.6944 50.0939 20.8149C50.0848 22.5822 49.6384 24.0989 48.7548 25.3652C47.8711 26.6314 46.6003 27.6016 44.9424 28.2757C43.2935 28.9498 41.3031 29.2869 38.971 29.2869C36.6298 29.2869 34.5893 28.9362 32.8493 28.2347C31.1094 27.5333 29.7566 26.4674 28.791 25.0372C27.8254 23.607 27.3289 21.7988 27.3016 19.6124H33.7785C33.8332 20.5143 34.0746 21.2658 34.5027 21.8671C34.9309 22.4683 35.5185 22.9238 36.2654 23.2335C37.0215 23.5433 37.8961 23.6981 38.889 23.6981C39.8 23.6981 40.5743 23.5751 41.212 23.3292C41.8588 23.0832 42.3552 22.7416 42.7014 22.3043C43.0476 21.8671 43.2252 21.3661 43.2343 20.8013C43.2252 20.2729 43.0612 19.822 42.7424 19.4485C42.4236 19.0659 41.9316 18.7379 41.2666 18.4646C40.6107 18.1822 39.7727 17.9226 38.7524 17.6858L35.8282 17.0025C33.405 16.4469 31.4966 15.5496 30.1028 14.3106C28.709 13.0626 28.0167 11.3773 28.0258 9.2548C28.0167 7.52397 28.4813 6.00721 29.4196 4.70454C30.3578 3.40186 31.656 2.38614 33.3139 1.65737C34.9719 0.928595 36.8621 0.564209 38.9847 0.564209C41.1528 0.564209 43.0339 0.933149 44.6281 1.67103C46.2314 2.3998 47.4749 3.42463 48.3585 4.74553C49.2421 6.06643 49.693 7.59685 49.7113 9.33678H43.2343Z" fill="#303030"/>
                <path d="M58.6991 28.9316H51.4296L60.8717 0.946814H69.8766L79.3187 28.9316H72.0492L65.4766 7.99767H65.258L58.6991 28.9316ZM57.7289 17.9181H72.9237V23.0559H57.7289V17.9181Z" fill="#303030"/>
                <path d="M77.9215 6.43992V0.946814H101.575V6.43992H93.0891V28.9316H86.4208V6.43992H77.9215Z" fill="#303030"/>
                <defs>
                    <linearGradient id="paint0_linear_36_959" x1="150.125" y1="-5.86693" x2="116.834" y2="-10.7627" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#66E2FF"/>
                    <stop offset="1" stop-color="#FF66C4"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_36_959" x1="150.125" y1="-5.86693" x2="116.834" y2="-10.7627" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#66E2FF"/>
                    <stop offset="1" stop-color="#FF66C4"/>
                    </linearGradient>
                </defs>
            </svg>
        </a>
    </div>
    
    <div class="nav-links desktop" role="menubar">
        {#each navLinks as link}
            <a 
                href={link.href} 
                class="nav-link" 
                role="menuitem"
                target={link.isExternal ? "_blank" : undefined}
                rel={link.isExternal ? "noopener" : undefined}
            >
                {#if link.isUnderlined}<u>{link.text}</u>{:else}{link.text}{/if}
            </a>
        {/each}
    </div>

    <div class="sign-in-button desktop">
        {@render buttons()}
    </div>

    <button class="dropdown-button mobile" class:isDropdownActive onclick={changeDropdown}>
        {#if isDropdownActive}
            {@render closeIcon()}
        {:else}
            {@render menuIcon()}
            {@render menuIcon()}
            {@render menuIcon()}
        {/if}
    </button>

    {#if isDropdownActive}
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_static_element_interactions -->
    <div class="dropdown-menu-overlay mobile" onclick={changeDropdown}>
        <div class="dropdown-menu">
            <div class="nav-links-mobile" role="menubar">
                {#each navLinks as link}
                    <a 
                        href={link.href} 
                        class="nav-link-mobile" 
                        role="menuitem"
                        target={link.isExternal ? "_blank" : undefined}
                        rel={link.isExternal ? "noopener" : undefined}
                    >
                        {#if link.isUnderlined}<u>{link.text}</u>{:else}{link.text}{/if}
                    </a>
                {/each}
            </div>
    
            <div class="sign-in-button-dropdown">
                {@render buttons()}
            </div>
        </div>
    </div>
    {/if}
</nav>

{#snippet buttons()}
    {#if !role}
        <a href="/sign-up">
            <Button>Sign In</Button>
        </a>
    {:else}
        <Button --button-bg-color="var(--rose)" onclick={async() => {
            await fetch("/api/sign-in", { method: "DELETE" });
            auth.signOut();
            posthog.reset();
            location.reload();
        }}>Sign Out</Button>

        <a href="/study">
            <Button>Study</Button>
        </a>
    {/if}
{/snippet}

<style>
    .mobile {
        display: none;
    }

    .navigation-bar {
        justify-content: space-between;
        align-items: center;
        background: var(--very-light-sky-blue, #f5fdff);
        border-bottom: 0.25rem solid var(--pitch-black, #000);
        display: flex;
        gap: 1rem;
        padding: 1.3125rem 4rem;
        font: 600 1rem "Open Sans", sans-serif;
        position: relative;
    }

    .logo {
        flex: 1 1 0px;
        width: fit-content;
    }
  
    .nav-links {
        display: flex;
        min-width: 15rem;
        align-items: center;
        gap: 2.5rem;
        white-space: nowrap;
        justify-content: center;
        margin: auto 0;
    }
  
    .nav-link {
        align-self: stretch;
        margin: auto 0;
    }
    
    .sign-in-button {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        flex: 1 1 0px;

        gap: 1rem;
    }

    @media (max-width: 1024px) {
        .desktop {
            display: none;
        }

        .mobile {
            display: initial;
        }

        .dropdown-button {
            display: flex;
            flex-direction: column;
            gap: 0.3125rem;
            background-color: white;
            border: 1px solid var(--pitch-black);
            border-radius: 0.25rem;
            padding: 0.5rem;
            box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
            position: relative;
        }

        .dropdown-menu-overlay {
            position: absolute;
            width: 100svw;
            height: 100svh;
            left: 0;
            bottom: 0;
            translate: 0 calc(100% + 0.25rem);
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 3;
        }

        .dropdown-button:active {
            box-shadow: none;
            translate: 0.25rem 0.25rem;
        }

        .isDropdownActive {
            padding: 0.375rem;
        }

        .dropdown-menu {
            display: flex;
            flex-direction: column;
        }

        .nav-links-mobile {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1rem 0;
            background-color: var(--light-sky-blue);
            border-bottom: 2px solid var(--pitch-black);
        }

        .nav-link-mobile {
            padding: 1rem 1.25rem;
            text-align: center;
        }

        .sign-in-button-dropdown {
            display: flex;
            justify-content: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--pitch-black);
            background-color: white;
            gap: 1rem;
        }
    }

    @media (max-width: 768px) {
        .navigation-bar {
            padding: 1.3125rem 1rem;
        }
    }
</style>