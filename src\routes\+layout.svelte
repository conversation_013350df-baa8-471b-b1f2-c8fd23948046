<script lang="ts">
    import '../app.css';
    import { onNavigate } from '$app/navigation';
	import { page } from '$app/state';
    import { user } from '$lib/firebase/auth.svelte';
	import posthog from 'posthog-js';
	import type { Snippet } from 'svelte';

    let { children }: { children: Snippet } = $props();

    $effect(() => {
        if ($user) {
            posthog.identify($user.uid, {
                email: $user.email,
                name: $user.displayName,
            })
        }
    })

    $effect(() => {
        if (page.data.affid) {
            posthog.capture('set_affiliate', {
                $set_once: { affid: page.data.affid }
            })
        }
    })
    
    onNavigate((navigation) => {
        if (!document.startViewTransition) return;

        return new Promise((resolve) => {
            document.startViewTransition(async () => {
                resolve();
                await navigation.complete;
            });
        });
    });
</script>

{@render children?.()}