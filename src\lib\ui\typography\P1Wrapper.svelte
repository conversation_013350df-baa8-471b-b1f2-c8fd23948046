<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<!-- 
    @component
    A wrapper component for P1 pargraphs on figma.
    
    Usage:
    ```tsx
    <P1Wrapper>
        <p>Lorem Ipsum</p>
    </P1Wrapper>
    ```
-->

<div class="wrapper">
    {@render children?.()}
</div>

<style>
    .wrapper {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        
        font-family: "Open Sans";
        font-size: 1.25rem;
        font-weight: 400;
        line-height: 1.875rem;
    }

    @media (max-width: 540px) {
        .wrapper {
            font-size: 1rem;
            line-height: 1.5rem;
        }
    }
</style>