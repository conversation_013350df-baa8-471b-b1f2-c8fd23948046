<script lang="ts">
	import { H3, P1 } from '$lib/ui';
	import SectionWrapper from '../SectionWrapper.svelte';

	interface Feature {
		title: string;
		description: string;
	}

	const features: Feature[] = [
		{
			title: 'More Challenging Tests',
			description:
				'Created based solely on SAT Suite Medium and Hard questions because the easy ones are just too easy.'
		},
		{
			title: 'Human-written Explanation',
			description:
				'Because, you know, College Board explanation are just long, repetitive and somewhat AI.'
		},
		{
			title: 'Test Result Analysis',
			description:
				'So you know for sure where you got wrong and can ACTUALLY target your weaknesses.'
		},
		{
			title: 'Study Plan',
			description:
				"Let's study productively instead of just doing random things hoping for a magical score raise."
		}
	];
</script>

<SectionWrapper
  --padding-top="5.25rem"
  --padding-bottom="8.125rem"
  --bg-color="var(--rose)"
>
  <div class="bubble-chat-container">
      <div class="bubble-chat-wait">
          <P1>WAIT ❗ Then do I need to count all questions manually!?</P1>
      </div>
      <div class="bubble-chat-no">
          <P1>Nooo, we already built a system for you!</P1>
      </div>
  </div>

  <div class="features-grid">
    {#each features as feature}
        <article class="feature-card">
            <H3>{feature.title}</H3>
            <P1>{feature.description}</P1>
        </article>
    {/each}
  </div>
</SectionWrapper>

<style>
    .features-grid {
        display: grid;
        grid-template-columns: minmax(20rem, 1fr) minmax(20rem, 1fr);
        gap: 2rem;
        margin: 0 auto;
        max-width: 62rem;
    }


    .feature-card {
        border-radius: 1rem;
        border: 0.1875rem solid #000;
        background: var(--light-purple);
        box-shadow: 0.25rem 0.25rem 0 0 #000;
        padding: 2rem;
        text-align: center;

        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .bubble-chat-container {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        margin-bottom: 5.4375rem;
        width: 100%;
        max-width: 45rem;
    }

    .bubble-chat-wait,
    .bubble-chat-no {
        border: 0.125rem solid  var(--pitch-black);
        padding: 1rem 2rem;
        width: fit-content;
      }

    .bubble-chat-wait {
      background: var(--light-rose);
      border-radius: 0 2rem 2rem 2rem;
    }

    .bubble-chat-no {
      background: var(--light-aquamarine);
      border-radius: 2rem 2rem 0 2rem;
      align-self: flex-end;
    }

    @media (max-width: 768px) {
        .features-grid {
          grid-template-columns: minmax(18rem, 1fr);
        }

        .feature-card {
          max-width: 32.5rem;
        }

        .bubble-chat-container {
          max-width: 31.5rem;
        }
    }
</style>
