<script>
	import { H2, H3, P1, P1<PERSON>rapper } from "$lib/ui";
	import SectionWrapper from "../SectionWrapper.svelte";

</script>
<SectionWrapper
	--bg-color="var(--yellow, #ffc800)"
	--padding-top="7.6875rem"
	--padding-bottom="7.6875rem"
>
	<div class="content-wrapper">
		<div class="title-wrapper">
			<H2>Actually,</H2>
		</div>
		<P1>what you need is to create a study system</P1>
		<div class="feature-box">
			<div class="feature-title-wrapper">
				<H3>where you can:</H3>
			</div>
			<P1Wrapper>
				<ul class="feature-list">
					<li>identify how many mistakes you made for each question type.</li>
					<li>understand your strengths and weaknesses.</li>
					<li>know where you need to work on to improve your score the most.</li>
					<li>create an effective study plan accordingly.</li>
				</ul>
			</P1Wrapper>
		</div>
	</div>
</SectionWrapper>

<style>
	.content-wrapper {
		display: flex;
		width: 25.875rem;
		max-width: 100%;
		flex-direction: column;
		margin-bottom: -1.5625rem;
	}

    .title-wrapper {
        text-align: center;
    }

	.feature-box {
		border: 0.1875rem solid var(--pitch-black, #000);
		background-color: #fff;
		display: flex;
		margin-top: 1.6875rem;
		flex-direction: column;
		padding: 3rem 2rem;
        box-shadow: 1.25rem 1.25rem;
	}

    .feature-title-wrapper {
        text-align: start;
    }

    .feature-list {
        margin-left: 1.75rem;
    }

	@media (max-width: 768px) {
		.feature-box {
			margin-right: 1rem;
		}
	}
</style>
