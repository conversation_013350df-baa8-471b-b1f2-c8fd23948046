<!-- 
    @component
    ## WalkMid
    The middle section of the walkthrough page. Can be either a math question or a reading question.
    
    ## Props
    - `i` - index of the question
    - `data` - the data of the test
    - `isMath` - whether the question is math or not
    - `isSPR` - whether the question is SPR or not
-->

<script>
    import WalkMath from '../ui/WalkMath.svelte';

	import TwoSide from "../ui/TwoSide.svelte";
    import MarkBar from "../ui/MarkBar.svelte";
	import MidRight from "../ui/MidRight.svelte";
	import MidVerbalLeft from "../ui/MidVerbalLeft.svelte";
	import Question from "../ui/Question.svelte";
	import WalkChoices from "../ui/WalkChoices.svelte";
	import WalkSolution from "../ui/WalkSolution.svelte";


    /** @type {{i: any, test: any, student: any, isMath: any, isSPR: any, m?: any}} */
    let {
        i,
        test,
        student,
        isMath,
        isSPR,
        m = -1
    } = $props();

    const isWalkthrough = true;
    
</script>

<!-- Middle -->
<TwoSide {isWalkthrough} {isMath} {isSPR}>
    <!-- If the question is R&W -->
    {#if !isMath}
    
        <MidVerbalLeft {i} data={test} {isWalkthrough}>
            <WalkSolution explanation={test.questions[i].explanation} {isMath}/>
        </MidVerbalLeft>

        <MidRight {isWalkthrough}>
            <!-- Index, Mark, and Elimination Bar -->
            <MarkBar {i} isSPR={false} {isWalkthrough}/>

            <!-- Questions -->
            <Question question={test.questions[i].question} isMath={false} {isWalkthrough}/>

            <WalkChoices {i} {test} {student} {isMath} {isSPR} isLeft={false} {m}/>
        </MidRight>

    <!-- If the question is math -->
    {:else}
        <WalkMath {i} {test} {student} {isMath} {isSPR} {m} />
    {/if}
    
</TwoSide>