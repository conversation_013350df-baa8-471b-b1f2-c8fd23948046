<script lang="ts">
	import { P2, H2, P1 } from "$lib/ui";

	import SectionWrapper from "../SectionWrapper.svelte";
	import ResourcesCard from "./ResourcesCard.svelte";
	import SubstackCard from "./SubstackCard.svelte";

	let { blogs } = $props();	
</script>

<SectionWrapper --padding-top="8.5rem" --padding-bottom="8.5rem" --bg-color="var(--pitch-black)">
	<div class="text-wrapper">
		<div class="resources-title">
			<P2 isBold={true} --text-color=var(--rose)>RESOURCES</P2>
		</div>
		<H2>Build Your Future</H2>
		<P1>Use technology as leverage to reach your target SAT score, get admitted, win that scholarship, and change your life.</P1>
	</div>
	<div class="resource-cards">
		<ResourcesCard 
			imageUrl=""
			title="DSAT16 QB"
			description="A collection of 5000+ unique SAT questions that you can't find anywhere else."
			buttonLink="/question-bank"
			isImageLeft
		/>
		<ResourcesCard 
			imageUrl=""
			title="DSAT16 Bootcamp"
			description="Intense SAT prep program to completely maximize your score in 8 weeks."
			isImageLeft={false}
		/>
	</div>
</SectionWrapper>

<style>
	.text-wrapper {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 1rem;
		color: white;
		text-align: center;
		max-width: 35rem;
		margin-bottom: 5.25rem;
	}

	.resources-title {
		width: fit-content;
		padding: 0.5rem 1rem;
		background-color: var(--light-rose);
		border-radius: 0.25rem;
		border: 1px solid var(--pitch-black);
		box-shadow: 0.25rem 0.25rem 0 var(--sky-blue);
	}

	.resource-cards {
		display: flex;
		flex-direction: column;
		justify-content: center;
		gap: 2rem;
	}
</style>