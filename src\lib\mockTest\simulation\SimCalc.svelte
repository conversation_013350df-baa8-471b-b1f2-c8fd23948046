<!-- 
    @component
    ## SimCalc
    A pop-up window to access the calculator (a DESMOS embed) in the simulation test.
    
    ## Props
    - `openCalculator` - A function to open the calculator.
-->

<script>
	import { onMount } from "svelte";

    
    let calculator = $state();

    /** @type {{openCalculator: any, isCalculatorCollapsed: any}} */
    let { openCalculator, isCalculatorCollapsed = $bindable() } = $props();
    
    function collapseCalculator() {
        calculator.style.width = isCalculatorCollapsed ? "975px" : "450px";
        isCalculatorCollapsed = !isCalculatorCollapsed;
    };

    function dragMouseDown(e) {
        var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

        e = e || window.event;
        e.preventDefault();
        // get the mouse cursor position at startup:
        pos3 = e.clientX;
        pos4 = e.clientY;

        document.onmouseup = () => {
            // stop moving when mouse button is released:
            document.onmouseup = null;
            document.onmousemove = null;
        };

        // call a function whenever the cursor moves:
        document.onmousemove = (e) => {
            e = e || window.event;
            e.preventDefault();

            // calculate the new cursor position:
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;

            // set the element's new position:
            calculator.style.top = (calculator.offsetTop - pos2 - 5) + "px";
            calculator.style.left = ( calculator.offsetLeft - pos1) + "px";
        };
    }

    // Load Desmos
    onMount(() => {
        let script = document.createElement('script');
        script.src = "https://www.desmos.com/api/v1.8/calculator.js?apiKey=dcb31709b452b1cf9dc26972add0fda6";
        script.async = true;

        script.onload = () => {
            var elt = document.getElementById('calculator');

            Desmos.GraphingCalculator(elt);
        }

        document.head.append(script);
    })

</script>

<div class="reference-container" bind:this={calculator}>
    <div class="reference-topbar" onmousedown={dragMouseDown}>
        <div class="reference-topbar-text">
            <p>Calculator</p>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
            <circle cx="2" cy="2" r="2" fill="white"/>
            <circle cx="2" cy="12" r="2" fill="white"/>
            <circle cx="10" cy="2" r="2" fill="white"/>
            <circle cx="10" cy="12" r="2" fill="white"/>
            <circle cx="18" cy="2" r="2" fill="white"/>
            <circle cx="18" cy="12" r="2" fill="white"/>
        </svg>
        <div class="reference-collapse">
            <button class="reference-collapse-button" onclick={collapseCalculator}>
                {#if isCalculatorCollapsed}
                    <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" viewBox="0 0 27 27" fill="none">
                        <rect width="27" height="27" rx="6" fill="white"/>
                        <path d="M6 5C5.44772 5 5 5.44772 5 6L5 15C5 15.5523 5.44772 16 6 16C6.55228 16 7 15.5523 7 15L7 7L15 7C15.5523 7 16 6.55228 16 6C16 5.44772 15.5523 5 15 5L6 5ZM5.29289 6.70711L11.6569 13.0711L13.0711 11.6569L6.70711 5.29289L5.29289 6.70711Z" fill="black"/>
                        <path d="M22.3633 23.3643C22.9156 23.3643 23.3633 22.9165 23.3633 22.3643L23.3633 13.3643C23.3633 12.812 22.9156 12.3643 22.3633 12.3643C21.811 12.3643 21.3633 12.812 21.3633 13.3643L21.3633 21.3643L13.3633 21.3643C12.811 21.3643 12.3633 21.812 12.3633 22.3643C12.3633 22.9165 12.811 23.3643 13.3633 23.3643L22.3633 23.3643ZM23.0704 21.6572L16.7064 15.2932L15.2922 16.7074L21.6562 23.0714L23.0704 21.6572Z" fill="black"/>
                    </svg>
                    <div class="reference-topbar-text reference-collapse-text">
                        <p>Expand</p>
                    </div>
                {:else}
                    <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" viewBox="0 0 27 27" fill="none">
                        <rect width="27" height="27" rx="6" fill="white"/>
                        <path d="M12.364 13.364C12.9162 13.364 13.364 12.9162 13.364 12.364L13.364 3.36396C13.364 2.81168 12.9162 2.36396 12.364 2.36396C11.8117 2.36396 11.364 2.81168 11.364 3.36396V11.364H3.36396C2.81168 11.364 2.36396 11.8117 2.36396 12.364C2.36396 12.9162 2.81168 13.364 3.36396 13.364L12.364 13.364ZM5.29289 6.70711L11.6569 13.0711L13.0711 11.6569L6.70711 5.29289L5.29289 6.70711Z" fill="black"/>
                        <path d="M15.9993 15.0003C15.447 15.0003 14.9993 15.448 14.9993 16.0003L14.9993 25.0003C14.9993 25.5526 15.447 26.0003 15.9993 26.0003C16.5516 26.0003 16.9993 25.5526 16.9993 25.0003V17.0003H24.9993C25.5516 17.0003 25.9993 16.5526 25.9993 16.0003C25.9993 15.448 25.5516 15.0003 24.9993 15.0003L15.9993 15.0003ZM23.0704 21.6572L16.7064 15.2932L15.2922 16.7074L21.6562 23.0714L23.0704 21.6572Z" fill="black"/>
                    </svg>
                    <div class="reference-topbar-text reference-collapse-text">
                        <p>Collapse</p>
                    </div>
                {/if}
            </button>

            <button class="reference-collapse-button" onclick={openCalculator}>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M13.4 12L19.7 5.7C20.1 5.3 20.1 4.7 19.7 4.3C19.3 3.9 18.7 3.9 18.3 4.3L12 10.6L5.7 4.3C5.3 3.9 4.7 3.9 4.3 4.3C3.9 4.7 3.9 5.3 4.3 5.7L10.6 12L4.3 18.3C4.1 18.5 4 18.7 4 19C4 19.6 4.4 20 5 20C5.3 20 5.5 19.9 5.7 19.7L12 13.4L18.3 19.7C18.5 19.9 18.7 20 19 20C19.3 20 19.5 19.9 19.7 19.7C20.1 19.3 20.1 18.7 19.7 18.3L13.4 12Z" fill="white"/>
                </svg>
            </button>
        </div>
    </div>
    <div id="calculator"></div>
</div>

<style>
    .reference-container {
        position: absolute;
        top: 90px;
        left: 64px;
        width: 450px;
        max-width: 100%;
        height: 650px;
        max-height: calc(100% - 91px);
        margin-top: 5px;
        background-color: #fff;
        border: 1px solid #505050;
        display: flex;
        flex-direction: column;
    }

    .reference-topbar {
        width: 100%;
        display: inline-flex;
        background-color: #000;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
    }

    .reference-topbar:hover {
        cursor: pointer;
    }

    .reference-topbar > * {
        flex: 1 1 0;
    }

    .reference-topbar-text {
        color: #FFF;
        font-family: "Inter";
        font-size: 17px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .reference-collapse {
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;
    }

    .reference-collapse-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        background: none;
    }

    .reference-collapse-text {
        margin-right: 22px;
    }

    #calculator {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1 0 auto;
    }
</style>