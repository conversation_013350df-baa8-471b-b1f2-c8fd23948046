<script>
	import { H1, P1 } from "$lib/ui";
	import WaitlistForm from "$lib/ui/WaitlistForm.svelte";
	import SectionWrapper from "../SectionWrapper.svelte";

</script>

<SectionWrapper --padding-top="8.5rem" --padding-bottom="8.5rem" --bg-color="var(--sky-blue)">
    <div class="heading-wrapper">
        <H1 --text-align="center">Digital SAT Prep Doesn't Have to Be Tedious. Let's Make It Fun.</H1>
    </div>
    <div class="p1-wrapper">
        <P1>Join 500+ others in our waitlist and you’ll be the first to know when DSAT16 releases Bootcamp, Reading16, and more new features.</P1>
        <WaitlistForm --button-bg-color="white" />
              
    </div>
</SectionWrapper>


<style>
    .heading-wrapper {
        max-width: 60rem;
        text-align: center;
        text-wrap: pretty;
    }

    .p1-wrapper {
        max-width: 35rem;
        margin-top: 1.5rem;
        display: flex;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
</style>