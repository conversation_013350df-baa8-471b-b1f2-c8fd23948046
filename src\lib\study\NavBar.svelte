<script lang="ts">
	import { onNavigate } from '$app/navigation';

	let { currentPath, role } = $props();

	// Mobile navigation state
	let isMobileMenuOpen = $state(false);

	// Toggle mobile menu
	function toggleMobileMenu() {
		isMobileMenuOpen = !isMobileMenuOpen;
		// Disable/enable body scrolling
		document.body.style.overflow = isMobileMenuOpen ? 'auto' : 'hidden';
	}

	// Close mobile menu
	function closeMobileMenu() {
		isMobileMenuOpen = false;
		// Re-enable body scrolling
		document.body.style.overflow = 'auto';
	}

	// Handle keyboard events
	function handleKeydown(event) {
		if (event.key === 'Escape' && isMobileMenuOpen) {
			closeMobileMenu();
		}
	}

	// Get current page name from path
	function getCurrentPageName(path: string) {
		if (path.includes('/dashboard')) return 'Dashboard';
		if (path.includes('/bootcamp')) return 'Bootcamp';
		if (path.includes('/question-bank')) return 'Question Bank';
		if (path.includes('/simulations')) return 'Simulations';
		if (path.includes('/vocab-tool')) return 'Vocab Tool';
		if (path.includes('/analysis')) return 'Analysis';
		return 'Study';
	}

	const navItems = [
		{
			svg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 16C7.39782 16 7.77936 15.842 8.06066 15.5607C8.34196 15.2794 8.5 14.8978 8.5 14.5C8.50488 14.4501 8.50488 14.3999 8.5 14.35L11.29 11.56H11.52H11.75L13.36 13.17C13.36 13.17 13.36 13.22 13.36 13.25C13.36 13.6478 13.518 14.0294 13.7993 14.3107C14.0806 14.592 14.4622 14.75 14.86 14.75C15.2578 14.75 15.6394 14.592 15.9207 14.3107C16.202 14.0294 16.36 13.6478 16.36 13.25V13.17L20 9.5C20.2967 9.5 20.5867 9.41203 20.8334 9.2472C21.08 9.08238 21.2723 8.84811 21.3858 8.57403C21.4993 8.29994 21.5291 7.99834 21.4712 7.70736C21.4133 7.41639 21.2704 7.14912 21.0607 6.93934C20.8509 6.72956 20.5836 6.5867 20.2926 6.52882C20.0017 6.47094 19.7001 6.50065 19.426 6.61418C19.1519 6.72771 18.9176 6.91997 18.7528 7.16664C18.588 7.41332 18.5 7.70333 18.5 8C18.4951 8.04988 18.4951 8.10012 18.5 8.15L14.89 11.76H14.73L13 10C13 9.60218 12.842 9.22064 12.5607 8.93934C12.2794 8.65804 11.8978 8.5 11.5 8.5C11.1022 8.5 10.7206 8.65804 10.4393 8.93934C10.158 9.22064 10 9.60218 10 10L7 13C6.60218 13 6.22064 13.158 5.93934 13.4393C5.65804 13.7206 5.5 14.1022 5.5 14.5C5.5 14.8978 5.65804 15.2794 5.93934 15.5607C6.22064 15.842 6.60218 16 7 16ZM20.5 20H3.5V3C3.5 2.73478 3.39464 2.48043 3.20711 2.29289C3.01957 2.10536 2.76522 2 2.5 2C2.23478 2 1.98043 2.10536 1.79289 2.29289C1.60536 2.48043 1.5 2.73478 1.5 3V21C1.5 21.2652 1.60536 21.5196 1.79289 21.7071C1.98043 21.8946 2.23478 22 2.5 22H20.5C20.7652 22 21.0196 21.8946 21.2071 21.7071C21.3946 21.5196 21.5 21.2652 21.5 21C21.5 20.7348 21.3946 20.4804 21.2071 20.2929C21.0196 20.1054 20.7652 20 20.5 20Z" fill="black"/>
                </svg>`,
			label: 'Dashboard',
			link: '/study'
		},
		{
			svg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21 10C21.1313 10 21.2614 9.97419 21.3827 9.92395C21.5041 9.8737 21.6143 9.80005 21.7072 9.70718C21.8 9.61432 21.8737 9.50406 21.9239 9.38272C21.9742 9.26138 22 9.13133 22 9V6C22.0001 5.79017 21.9341 5.58565 21.8114 5.41544C21.6887 5.24524 21.5155 5.11799 21.3164 5.05176L12.3164 2.05176C12.111 1.9834 11.889 1.9834 11.6836 2.05176L2.68359 5.05176C2.48449 5.11799 2.31131 5.24524 2.18861 5.41544C2.0659 5.58565 1.99991 5.79017 2 6V9C1.99997 9.13133 2.02581 9.26138 2.07605 9.38272C2.12629 9.50406 2.19995 9.61432 2.29282 9.70718C2.38568 9.80005 2.49594 9.8737 2.61728 9.92395C2.73862 9.97419 2.86867 10 3 10H4V17.1843C3.41674 17.3897 2.91137 17.7707 2.55327 18.2748C2.19517 18.779 2.0019 19.3816 2 20V22C1.99997 22.1313 2.02581 22.2614 2.07605 22.3827C2.12629 22.5041 2.19995 22.6143 2.29282 22.7072C2.38568 22.8 2.49594 22.8737 2.61728 22.9239C2.73862 22.9742 2.86867 23 3 23H21C21.1313 23 21.2614 22.9742 21.3827 22.9239C21.5041 22.8737 21.6143 22.8 21.7072 22.7072C21.8 22.6143 21.8737 22.5041 21.9239 22.3827C21.9742 22.2614 22 22.1313 22 22V20C21.9981 19.3816 21.8048 18.779 21.4467 18.2748C21.0886 17.7707 20.5833 17.3897 20 17.1843V10H21ZM20 21H4V20C4.00026 19.7349 4.10571 19.4807 4.29319 19.2932C4.48066 19.1057 4.73486 19.0003 5 19H19C19.2651 19.0003 19.5193 19.1057 19.7068 19.2932C19.8943 19.4807 19.9997 19.7349 20 20V21ZM6 17V10H8V17H6ZM10 17V10H14V17H10ZM16 17V10H18V17H16ZM4 8V6.7207L12 4.0537L20 6.7207V8H4Z" fill="black"/>
</svg>
`,
			label: 'Question Bank',
			link: '/study/question-bank'
		},
		{
			svg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19 2H5C4.20435 2 3.44129 2.31607 2.87868 2.87868C2.31607 3.44129 2 4.20435 2 5V15C2 15.7956 2.31607 16.5587 2.87868 17.1213C3.44129 17.6839 4.20435 18 5 18H8V20H5C4.73478 20 4.48043 20.1054 4.29289 20.2929C4.10536 20.4804 4 20.7348 4 21C4 21.2652 4.10536 21.5196 4.29289 21.7071C4.48043 21.8946 4.73478 22 5 22H19C19.2652 22 19.5196 21.8946 19.7071 21.7071C19.8946 21.5196 20 21.2652 20 21C20 20.7348 19.8946 20.4804 19.7071 20.2929C19.5196 20.1054 19.2652 20 19 20H16V18H19C19.7956 18 20.5587 17.6839 21.1213 17.1213C21.6839 16.5587 22 15.7956 22 15V5C22 4.20435 21.6839 3.44129 21.1213 2.87868C20.5587 2.31607 19.7956 2 19 2ZM14 20H10V18H14V20ZM20 15C20 15.2652 19.8946 15.5196 19.7071 15.7071C19.5196 15.8946 19.2652 16 19 16H5C4.73478 16 4.48043 15.8946 4.29289 15.7071C4.10536 15.5196 4 15.2652 4 15V14H20V15ZM20 12H4V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H19C19.2652 4 19.5196 4.10536 19.7071 4.29289C19.8946 4.48043 20 4.73478 20 5V12Z" fill="black"/>
</svg>

`,
			label: 'Simulations',
			link: '/study/simulations'
		},
		{
			svg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 16C6.80222 16 6.60888 16.0586 6.44443 16.1685C6.27998 16.2784 6.15181 16.4346 6.07612 16.6173C6.00043 16.8 5.98063 17.0011 6.01921 17.1951C6.0578 17.3891 6.15304 17.5673 6.29289 17.7071C6.43275 17.847 6.61093 17.9422 6.80491 17.9808C6.99889 18.0194 7.19996 17.9996 7.38268 17.9239C7.56541 17.8482 7.72159 17.72 7.83147 17.5556C7.94135 17.3911 8 17.1978 8 17C8 16.7348 7.89464 16.4804 7.70711 16.2929C7.51957 16.1054 7.26522 16 7 16ZM19.06 12L20.29 10.77C20.8518 10.2075 21.1674 9.445 21.1674 8.65C21.1674 7.855 20.8518 7.0925 20.29 6.53L17.46 3.71C16.8975 3.1482 16.135 2.83264 15.34 2.83264C14.545 2.83264 13.7825 3.1482 13.22 3.71L12 4.94C11.9843 4.15479 11.6613 3.40706 11.1004 2.85736C10.5395 2.30766 9.78536 1.99984 9 2H5C4.20435 2 3.44129 2.31607 2.87868 2.87868C2.31607 3.44129 2 4.20435 2 5V19C2 19.7956 2.31607 20.5587 2.87868 21.1213C3.44129 21.6839 4.20435 22 5 22H19C19.7956 22 20.5587 21.6839 21.1213 21.1213C21.6839 20.5587 22 19.7956 22 19V15C22.0002 14.2146 21.6923 13.4605 21.1426 12.8996C20.5929 12.3387 19.8452 12.0157 19.06 12ZM10 19C10 19.2652 9.89464 19.5196 9.70711 19.7071C9.51957 19.8946 9.26522 20 9 20H5C4.73478 20 4.48043 19.8946 4.29289 19.7071C4.10536 19.5196 4 19.2652 4 19V5C4 4.73478 4.10536 4.48043 4.29289 4.29289C4.48043 4.10536 4.73478 4 5 4H9C9.26522 4 9.51957 4.10536 9.70711 4.29289C9.89464 4.48043 10 4.73478 10 5V19ZM12 7.76L14.64 5.12C14.8274 4.93375 15.0808 4.82921 15.345 4.82921C15.6092 4.82921 15.8626 4.93375 16.05 5.12L18.88 8C19.0662 8.18736 19.1708 8.44081 19.1708 8.705C19.1708 8.96919 19.0662 9.22264 18.88 9.41L16 12.29L12 16.24V7.76ZM20 19C20 19.2652 19.8946 19.5196 19.7071 19.7071C19.5196 19.8946 19.2652 20 19 20H11.82C11.9226 19.7036 11.9799 19.3935 11.99 19.08L17.07 14H19C19.2652 14 19.5196 14.1054 19.7071 14.2929C19.8946 14.4804 20 14.7348 20 15V19Z" fill="black"/>
</svg>
`,
			label: 'Vocab Tool',
			link: '/study/vocab-tool'
		},
		{
			svg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.1701 2.06012C20.4533 1.93665 19.7274 1.8731 19.0001 1.87012C16.5184 1.86809 14.0886 2.57969 12.0001 3.92012C9.90623 2.59729 7.47674 1.90315 5.00008 1.92012C4.27273 1.9231 3.54689 1.98665 2.83008 2.11012C2.59529 2.1506 2.38267 2.27358 2.2305 2.45691C2.07833 2.64025 1.99662 2.87189 2.00008 3.11012V15.1101C1.99794 15.2571 2.02821 15.4027 2.08873 15.5366C2.14926 15.6705 2.23855 15.7894 2.35026 15.8849C2.46197 15.9803 2.59334 16.05 2.73504 16.089C2.87674 16.1279 3.02528 16.1351 3.17008 16.1101C4.6031 15.862 6.07129 15.9032 7.48815 16.2312C8.90501 16.5592 10.2419 17.1675 11.4201 18.0201L11.5401 18.0901H11.6501C11.761 18.1363 11.8799 18.1601 12.0001 18.1601C12.1202 18.1601 12.2392 18.1363 12.3501 18.0901H12.4601L12.5801 18.0201C13.75 17.1484 15.0831 16.5205 16.5002 16.1735C17.9174 15.8265 19.3897 15.7675 20.8301 16.0001C20.9749 16.0251 21.1234 16.0179 21.2651 15.979C21.4068 15.94 21.5382 15.8703 21.6499 15.7749C21.7616 15.6794 21.8509 15.5605 21.9114 15.4266C21.972 15.2927 22.0022 15.1471 22.0001 15.0001V3.00012C21.9897 2.77228 21.9017 2.55483 21.7507 2.38386C21.5998 2.21289 21.3949 2.09866 21.1701 2.06012ZM11.0001 15.3501C9.14997 14.3768 7.0906 13.8688 5.00008 13.8701C4.67008 13.8701 4.34008 13.8701 4.00008 13.8701V3.87012C4.33314 3.85093 4.66703 3.85093 5.00008 3.87012C7.13347 3.86776 9.2203 4.49381 11.0001 5.67012V15.3501ZM20.0001 13.9101C19.6601 13.9101 19.3301 13.9101 19.0001 13.9101C16.9096 13.9088 14.8502 14.4168 13.0001 15.3901V5.67012C14.7799 4.49381 16.8667 3.86776 19.0001 3.87012C19.3331 3.85093 19.667 3.85093 20.0001 3.87012V13.9101ZM21.1701 18.0601C20.4533 17.9367 19.7274 17.8731 19.0001 17.8701C16.5184 17.8681 14.0886 18.5797 12.0001 19.9201C9.9116 18.5797 7.48172 17.8681 5.00008 17.8701C4.27273 17.8731 3.54689 17.9367 2.83008 18.0601C2.69991 18.0808 2.57511 18.127 2.46285 18.196C2.35059 18.2651 2.25309 18.3557 2.17595 18.4625C2.09882 18.5694 2.04357 18.6905 2.01339 18.8188C1.98321 18.9471 1.97869 19.0801 2.00008 19.2101C2.0509 19.4698 2.20257 19.6988 2.42188 19.8468C2.64118 19.9949 2.91022 20.05 3.17008 20.0001C4.6031 19.752 6.07129 19.7932 7.48815 20.1212C8.90501 20.4492 10.2419 21.0575 11.4201 21.9101C11.5894 22.0307 11.7922 22.0955 12.0001 22.0955C12.208 22.0955 12.4107 22.0307 12.5801 21.9101C13.7583 21.0575 15.0952 20.4492 16.512 20.1212C17.9289 19.7932 19.3971 19.752 20.8301 20.0001C21.0899 20.05 21.359 19.9949 21.5783 19.8468C21.7976 19.6988 21.9493 19.4698 22.0001 19.2101C22.0215 19.0801 22.017 18.9471 21.9868 18.8188C21.9566 18.6905 21.9014 18.5694 21.8242 18.4625C21.7471 18.3557 21.6496 18.2651 21.5373 18.196C21.4251 18.127 21.3003 18.0808 21.1701 18.0601Z" fill="black"/>
</svg>
`,
			label: 'Bootcamp',
			link: '/bootcamp'
		}
	];

	let selectedPage = $state(navItems.findIndex((item) => item.link === currentPath));

	onNavigate(({ to }) => {
		selectedPage = navItems.findIndex((item) => item.link === to.route.id);
		closeMobileMenu();
	});
</script>

<svelte:window on:keydown={handleKeydown} />

<!-- Mobile top navbar -->
<div class="mobile-top-navbar">
	<button
		class="mobile-toggle"
		onclick={toggleMobileMenu}
		aria-label={isMobileMenuOpen ? 'Close navigation menu' : 'Open navigation menu'}
		aria-expanded={isMobileMenuOpen}
	>
		<span class="hamburger-line"></span>
		<span class="hamburger-line"></span>
		<span class="hamburger-line"></span>
	</button>
	<div class="current-page-name">
		{getCurrentPageName(currentPath)}
	</div>
</div>

<!-- Mobile backdrop -->
{#if isMobileMenuOpen}
	<div
		class="mobile-backdrop"
		onclick={closeMobileMenu}
		onkeydown={(e) => e.key === 'Enter' && closeMobileMenu()}
		role="button"
		tabindex="0"
		aria-label="Close navigation menu"
	></div>
{/if}

<nav class="navbar" class:mobile-open={isMobileMenuOpen}>
	<div class="navbar-header">
		<svg
			class="dsat16-icon-expand"
			width="102"
			height="30"
			viewBox="0 0 102 30"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M10.344 29.0061H0V1.02128H10.3303C13.1816 1.02128 15.6367 1.58152 17.6955 2.702C19.7633 3.81338 21.3575 5.41667 22.478 7.51189C23.5985 9.598 24.1587 12.094 24.1587 15C24.1587 17.9151 23.5985 20.4202 22.478 22.5155C21.3666 24.6107 19.777 26.2185 17.7091 27.339C15.6412 28.4504 13.1862 29.0061 10.344 29.0061ZM6.7639 23.2397H10.0844C11.6512 23.2397 12.9767 22.9755 14.0607 22.4471C15.1539 21.9097 15.9783 21.0397 16.534 19.8372C17.0988 18.6256 17.3812 17.0132 17.3812 15C17.3812 12.9868 17.0988 11.3835 16.534 10.1901C15.9692 8.98765 15.1357 8.12224 14.0334 7.59388C12.9402 7.05641 11.592 6.78767 9.98871 6.78767H6.7639V23.2397Z"
				fill="black"
			/>
			<path
				d="M43.2343 9.41125C43.1432 8.40919 42.7378 7.63031 42.0182 7.07463C41.3076 6.50983 40.2919 6.22743 38.971 6.22743C38.0965 6.22743 37.3677 6.3413 36.7847 6.56904C36.2017 6.79678 35.7644 7.11107 35.4729 7.51189C35.1814 7.9036 35.0311 8.35453 35.022 8.86467C35.0038 9.28371 35.0857 9.65265 35.2679 9.97149C35.4592 10.2903 35.7325 10.5727 36.0878 10.8187C36.4522 11.0555 36.8895 11.2651 37.3996 11.4473C37.9097 11.6294 38.4836 11.7889 39.1213 11.9255L41.5263 12.4721C42.9109 12.7727 44.1316 13.1735 45.1883 13.6746C46.2542 14.1756 47.1469 14.7723 47.8666 15.4646C48.5953 16.1569 49.1465 16.954 49.52 17.8559C49.8935 18.7577 50.0848 19.7689 50.0939 20.8894C50.0848 22.6567 49.6384 24.1734 48.7548 25.4396C47.8711 26.7059 46.6003 27.6761 44.9424 28.3502C43.2935 29.0243 41.3031 29.3613 38.971 29.3613C36.6298 29.3613 34.5893 29.0106 32.8493 28.3092C31.1094 27.6077 29.7566 26.5419 28.791 25.1117C27.8254 23.6815 27.3289 21.8732 27.3016 19.6869H33.7785C33.8332 20.5888 34.0746 21.3403 34.5027 21.9415C34.9309 22.5428 35.5185 22.9983 36.2654 23.308C37.0215 23.6177 37.8961 23.7726 38.889 23.7726C39.8 23.7726 40.5743 23.6496 41.212 23.4036C41.8588 23.1577 42.3552 22.8161 42.7014 22.3788C43.0476 21.9415 43.2252 21.4405 43.2343 20.8757C43.2252 20.3474 43.0612 19.8964 42.7424 19.5229C42.4236 19.1403 41.9316 18.8124 41.2666 18.5391C40.6107 18.2567 39.7727 17.9971 38.7524 17.7602L35.8282 17.077C33.405 16.5213 31.4966 15.624 30.1028 14.3851C28.709 13.1371 28.0167 11.4518 28.0258 9.32926C28.0167 7.59843 28.4813 6.08168 29.4196 4.779C30.3578 3.47632 31.656 2.4606 33.3139 1.73183C34.9719 1.00306 36.8621 0.638672 38.9847 0.638672C41.1528 0.638672 43.0339 1.00761 44.6281 1.74549C46.2314 2.47426 47.4749 3.4991 48.3585 4.81999C49.2421 6.14089 49.693 7.67131 49.7113 9.41125H43.2343Z"
				fill="black"
			/>
			<path
				d="M58.6991 29.0061H51.4296L60.8717 1.02128H69.8766L79.3187 29.0061H72.0492L65.4766 8.07213H65.258L58.6991 29.0061ZM57.7289 17.9925H72.9237V23.1304H57.7289V17.9925Z"
				fill="black"
			/>
			<path
				d="M77.9215 6.51438V1.02128H101.575V6.51438H93.0891V29.0061H86.4208V6.51438H77.9215Z"
				fill="black"
			/>
		</svg>

		<svg
			class="dsat16-icon"
			width="56"
			height="30"
			viewBox="0 0 56 30"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M20.1381 0.39924V29.6008H13.0943V6.98669H12.9232L6.39282 10.9791V4.87643L13.5934 0.39924H20.1381Z"
				fill="url(#paint0_linear_4598_2846)"
			/>
			<path
				d="M38.2999 30C36.7029 30 35.1725 29.7433 33.7086 29.23C32.2448 28.7072 30.9425 27.8802 29.8018 26.749C28.6611 25.6084 27.7628 24.116 27.1069 22.2719C26.451 20.4183 26.1278 18.1606 26.1374 15.499C26.1469 13.0941 26.4415 10.9363 27.0214 9.02567C27.6012 7.10551 28.4282 5.47529 29.5024 4.13498C30.586 2.79468 31.8788 1.77281 33.3807 1.06939C34.8921 0.356464 36.5794 0 38.4425 0C40.4862 0 42.2875 0.39924 43.8465 1.19772C45.4149 1.98669 46.6697 3.05133 47.6107 4.39163C48.5518 5.72243 49.1079 7.20532 49.279 8.8403H42.3351C42.1259 7.91825 41.6649 7.21958 40.952 6.7443C40.2486 6.2595 39.4121 6.01711 38.4425 6.01711C36.6554 6.01711 35.3199 6.79182 34.4358 8.34125C33.5613 9.89068 33.1145 11.9629 33.0955 14.558H33.2809C33.6801 13.6835 34.2552 12.9325 35.0062 12.3051C35.7571 11.6778 36.6174 11.1977 37.587 10.865C38.5661 10.5228 39.6022 10.3517 40.6953 10.3517C42.4444 10.3517 43.9891 10.7557 45.3294 11.5637C46.6697 12.3717 47.72 13.4791 48.4805 14.8859C49.241 16.2833 49.6164 17.885 49.6069 19.6911C49.6164 21.7253 49.1412 23.5219 48.1811 25.0808C47.221 26.6302 45.8902 27.8375 44.1887 28.7025C42.4967 29.5675 40.5337 30 38.2999 30ZM38.2571 24.5817C39.1221 24.5817 39.8969 24.3774 40.5813 23.9686C41.2657 23.5599 41.8027 23.0038 42.1925 22.3004C42.5822 21.597 42.7723 20.8032 42.7628 19.9192C42.7723 19.0257 42.5822 18.2319 42.1925 17.538C41.8123 16.8441 41.2799 16.2928 40.5955 15.884C39.9206 15.4753 39.1459 15.2709 38.2714 15.2709C37.6345 15.2709 37.0404 15.3897 36.4891 15.6274C35.9377 15.865 35.4577 16.1977 35.0489 16.6255C34.6497 17.0437 34.336 17.538 34.1079 18.1084C33.8797 18.6692 33.7609 19.2776 33.7514 19.9335C33.7609 20.7985 33.9605 21.5827 34.3503 22.2861C34.74 22.9895 35.2723 23.5504 35.9472 23.9686C36.6221 24.3774 37.3921 24.5817 38.2571 24.5817Z"
				fill="url(#paint1_linear_4598_2846)"
			/>
			<defs>
				<linearGradient
					id="paint0_linear_4598_2846"
					x1="51.6609"
					y1="-5.86693"
					x2="18.3696"
					y2="-10.7627"
					gradientUnits="userSpaceOnUse"
				>
					<stop stop-color="#66E2FF" />
					<stop offset="1" stop-color="#FF66C4" />
				</linearGradient>
				<linearGradient
					id="paint1_linear_4598_2846"
					x1="51.6609"
					y1="-5.86693"
					x2="18.3696"
					y2="-10.7627"
					gradientUnits="userSpaceOnUse"
				>
					<stop stop-color="#66E2FF" />
					<stop offset="1" stop-color="#FF66C4" />
				</linearGradient>
			</defs>
		</svg>
	</div>

	<ul class="nav-items">
		{#each navItems as item, index}
			{#if item.link !== '/bootcamp' || role === 'Pro'}
				<li>
					<a href={item.link}>
						<button class="nav-link" class:active={selectedPage === index}>
							<span class="icon">
								{@html item.svg}
							</span>
							<span class="nav-link-text">{item.label}</span>
						</button>
					</a>
				</li>
			{/if}
		{/each}
	</ul>
</nav>

<style>
	.navbar {
		position: fixed;
		left: 0;
		top: 0;
		height: max(100vh, 100%);
		background-color: var(--white);
		border-right: 2px solid var(--pitch-black);
		padding: 6px 10px;
		width: 4.75rem;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 2rem;
		transition: width 0.3s ease;
		overflow: hidden;
		z-index: 1000;
	}

	.navbar:hover {
		width: 15rem;
	}

	.navbar-header {
		margin-top: 30px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.nav-items {
		list-style: none;
		padding: 0;
		margin-top: 3rem;
		display: flex;
		flex-direction: column;
		gap: 1rem;
		cursor: pointer;
		width: 100%;
	}

	.nav-link {
		display: flex;
		align-items: center;
		padding: 0.75rem;
		text-decoration: none;
		color: var(--charcoal);
		border-radius: 1rem;
		transition: background-color 0.2s ease;
		background-color: var(--white);
		width: 100%;
		height: 56px;
		border: none;
		cursor: pointer;
		gap: 0.75rem;
	}

	.nav-link:hover {
		background-color: var(--light-sky-blue);
	}

	.nav-link-text {
		color: #000;
		font-family: Inter;
		font-size: 20px;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		white-space: nowrap;
		opacity: 0;
		transform: translateX(-10px);
		transition:
			opacity 0.3s ease,
			transform 0.3s ease;
	}

	.dsat16-icon {
		transform: translateX(-50px);
		transition: transform 0.3s ease;
	}

	.dsat16-icon-expand {
		opacity: 0;
		transform: translateX(-50px);
		transition:
			opacity 0.3s ease,
			transform 0.3s ease;
	}

	.navbar:hover .nav-link-text,
	.navbar:hover .dsat16-icon-expand,
	.navbar:hover .dsat16-icon {
		opacity: 1;
		transform: translateX(0);
	}

	.nav-link.active {
		background-color: var(--sky-blue);
		border: 1.5px solid black;
		border-radius: 1rem;
		white-space: nowrap;
		cursor: pointer;
		box-shadow: 2px 2px black;
	}

	.icon {
		display: flex;
		justify-content: center;
		align-items: center;
		min-width: 24px;
	}

	/* Mobile top navbar - hidden by default */
	.mobile-top-navbar {
		display: none;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rem;
		background: var(--white);
		border-bottom: 1px solid var(--pitch-black);
		z-index: 2;
		align-items: center;
		padding: 0 1rem;
		gap: 1rem;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.mobile-toggle {
		background: var(--white);
		border: 1px solid var(--pitch-black);
		border-radius: 0.5rem;
		padding: 0.5rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		cursor: pointer;
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
		width: 2.5rem;
		height: 2.5rem;
		justify-content: center;
		align-items: center;
	}

	.mobile-toggle:active {
		box-shadow: none;
		transform: translate(0.25rem, 0.25rem);
	}

	.hamburger-line {
		width: 1.25rem;
		height: 0.125rem;
		background-color: var(--pitch-black);
		transition: all 0.3s ease;
	}

	.current-page-name {
		font-family: 'Inter';
		font-size: 1.25rem;
		font-weight: 600;
		color: var(--pitch-black);
	}

	/* Mobile backdrop */
	.mobile-backdrop {
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
		cursor: pointer;
	}

	/* Mobile responsive styles */
	@media (max-width: 960px) {
		.mobile-top-navbar {
			display: flex;
		}

		.mobile-backdrop {
			display: block;
		}

		.navbar {
			transform: translateX(-100%);
			transition: transform 0.3s ease;
			z-index: 1000;
		}

		.navbar.mobile-open {
			transform: translateX(0);
		}

		.navbar:hover {
			width: 4.75rem; /* Disable hover expansion on mobile */
		}

		.navbar:hover .nav-link-text,
		.navbar:hover .dsat16-icon-expand,
		.navbar:hover .dsat16-icon {
			opacity: 0;
			transform: translateX(-50px);
		}

		.navbar.mobile-open .nav-link-text,
		.navbar.mobile-open .dsat16-icon-expand,
		.navbar.mobile-open .dsat16-icon {
			opacity: 1;
			transform: translateX(0);
		}

		.navbar.mobile-open {
			width: 15rem;
		}
	}
</style>
