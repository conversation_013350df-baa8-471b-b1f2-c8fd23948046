<!-- 
    @component
    P2 pargraphs on figma.
    
    Usage:
    ```tsx
    <P2>Lorem Ipsum</P2>
    ```
-->
<script>
    /** @type {{isBold?: boolean, isEllipsis?: boolean, children?: import('svelte').Snippet}} */
    let { isBold = false, isEllipsis = false, children } = $props();
</script>

<p class:bold={isBold} class:ellipsis={isEllipsis}>
    {@render children?.()}
</p>

<style>
    p { 
        font-family: "Open Sans";
        font-size: 1.125rem;
        font-weight: 450;
        line-height: 1.6875rem;
        color: var(--text-color, --pitch-black);
        text-decoration-color: var(--text-color, --pitch-black);
    }
    
    .bold {
        font-weight: 600;
        font-family: "Inter";
    }   

    .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    @media (max-width: 768px) {
        p {
            font-size: 0.9375rem;
            line-height: 1.375rem;
        }
    }
</style>