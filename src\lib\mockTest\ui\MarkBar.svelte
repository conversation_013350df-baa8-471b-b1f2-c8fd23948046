<script lang="ts">
	import MarkBarUi from "$lib/ui/MarkBarUI.svelte";
	import type { <PERSON><PERSON><PERSON>Hand<PERSON> } from "svelte/elements";

    interface Props {
        isMarked?: boolean[];
        setMarked?: MouseEventHandler<HTMLButtonElement>;
        i: number;
        toggleElimination?: MouseEventHandler<HTMLButtonElement>;
        isEliminateTool: boolean;
        isSPR: boolean;
        isWalkthrough?: boolean;
    }

    let {
        isMarked = [],
        setMarked = () => null,
        i,
        toggleElimination = () => null,
        isEliminateTool,
        isSPR,
        isWalkthrough = false
    }: Props = $props();
</script>

<MarkBarUi isMarked={isMarked[i]} {setMarked} {i} {toggleElimination} {isEliminateTool} {isSPR} {isWalkthrough} />