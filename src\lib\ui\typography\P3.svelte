<!-- 
    @component
    P3 pargraphs on figma.
    
    Usage:
    ```tsx
    <P3>Lorem Ipsum</P3>
    ```
-->
<script>
    /** @type {{isBold?: boolean, children?: import('svelte').Snippet}} */
    let { isBold = false, children } = $props();
</script>

<p class:bold={isBold}>
    {@render children?.()}
</p>

<style>
    p { 
        font-family: "Open Sans";
        font-size: 1rem;
        font-weight: 450;
        line-height: 1.5rem;
        color: var(--text-color, --pitch-black);
        text-decoration-color: var(--text-color, --pitch-black);
    }
    
    .bold {
        font-weight: 700;
    }   

    @media (max-width: 768px) {
        p {
            font-size: 0.875rem;
            line-height: 1.3125rem;
        }
    }
</style>