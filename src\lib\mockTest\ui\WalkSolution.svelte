<script>
    /** @type {{explanation: any, isMath: any}} */
    let { explanation, isMath } = $props();
</script>

<div class="solution" class:solution--right={isMath}>
    <div class="solution-title">Solution:</div>
        {#key explanation}
            <div class="solution-passage">
                {@html explanation}
            </div>
        {/key}
</div>

<style>
    .solution {
        margin-top: 32px;
    }

    .solution--right {
        margin: 32px 32px 16px 32px;
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .solution-title {
        font-size: 18px;
        font-weight: 700;
        font-family: "Merriweather";
    }

    .solution-passage {
        display: flex;
        flex-direction: column;
        gap: 8px;
        color: var(--Charcoal, #333);
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }
</style>