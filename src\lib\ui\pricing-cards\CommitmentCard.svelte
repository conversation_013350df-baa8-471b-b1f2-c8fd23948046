<script>
	import PricingCard from "./PricingCard.svelte";

    const commitment = [
        {
            text: "3 DSAT16 Simulation",
            subtext: "(98 Questions, 2 hours 24 minutes)"
        },
        {
            text: "Full answer + Explanation",
            subtext: null
        },
        {
            text: "Comprehensive Analysis",
            subtext: null
        },
        {
            text: "Weekly Study Plan",
            subtext: null
        },
        {
            text: "Bonus: ebooks and materials to build confidence for test day.",
            subtext: null
        },
    ]
</script>


<PricingCard 
    label="Commitment"
    description="For dedicated students who want to commit to studying and see significant changes in their SAT score."
    price="€25.16"
    oldPrice="€37"
    checklist={commitment}
    priceId="price_1QUtPBJPIuRILQq66MTVeZHY"
    --label-bg-color="linear-gradient( 90deg, var(--light-sky-blue), var(--light-rose) )"
    --icon-color=var(--aquamarine)
    --button-bg-color="linear-gradient( 90deg, var(--sky-blue), var(--rose) )"
    --button-text-color=white
/>