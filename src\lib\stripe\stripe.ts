import { collection, doc, DocumentReference, onSnapshot, setDoc } from "firebase/firestore";
import { db } from "$lib/firebase";
import type { User } from "firebase/auth";

interface CheckoutSessionConfig {
    mode: "payment" | "subscription";
    priceId: string;
    uid: string;
    successUrl: string;
    cancelUrl: string;
    promotionCode?: string | null;
}

const handleCheckoutSession = (docRef: DocumentReference) => {
    onSnapshot(docRef, (snap) => {
        const { error, url } = snap.data();
        
        if (error) {
            alert("An error occurred. Please try again.");
        }

        if (url) {
            window.location.assign(url);
        }
    });
};

const createCheckoutSession = async ({
    mode,
    priceId,
    uid,
    successUrl,
    cancelUrl,
    promotionCode = null
}: CheckoutSessionConfig) => {
    const docRef = doc(collection(db, "users/" + uid + "/checkout_sessions"));
    
    await setDoc(docRef, {
        mode,
        price: priceId,
        success_url: successUrl,
        cancel_url: cancelUrl,
        allow_promotion_codes: true,
        ...(promotionCode && { promotion_code: promotionCode }),
    });

    handleCheckoutSession(docRef);
};

export const createOnetimePaymentSession = async (priceId: string, uid: string, promotionCode: string | null = null) => {
    await createCheckoutSession({
        mode: "payment",
        priceId,
        uid,
        successUrl: "https://www.dsat16.com/thank-you?session_id={CHECKOUT_SESSION_ID}",
        cancelUrl: "https://www.dsat16.com/simulation#pricing",
        promotionCode
    });
};

export const createSubscriptionSession = async (priceId: string, uid: string, promotionCode: string | null = null) => {
    await createCheckoutSession({
        mode: "subscription",
        priceId,
        uid,
        successUrl: "https://www.dsat16.com/question-bank",
        cancelUrl: "https://www.dsat16.com/question-bank",
        promotionCode
    });
};

export async function getCustomClaimRole(user: User) {
    await user.getIdToken(true);
    const decodedToken = await user.getIdTokenResult();
    return decodedToken.claims.role;
}