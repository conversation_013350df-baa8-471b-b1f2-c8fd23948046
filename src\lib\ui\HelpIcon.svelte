<script lang="ts">
	import type { Snippet } from 'svelte';
	import { innerWidth } from 'svelte/reactivity/window';
	
	let { title = '', children }: { title?: string; children: Snippet } = $props();
</script>

<span class="help-icon-wrapper" aria-label={title}>
	{@render children()}
	{#if title && innerWidth.current > 540}
		<span class="help-tooltip">{title}</span>
	{/if}
</span>

<style>
	.help-icon-wrapper {
		position: relative;
		display: inline-block;
		cursor: help;
	}
	
	.help-tooltip {
		visibility: hidden;
		background: var(--bg-color, var(--white));
		color: var(--pitch-black);
		text-align: center;
		padding: 0.5rem 0.75rem;
		position: absolute;
		z-index: 10;
		bottom: 125%;
		left: 50%;
		translate: -50% 0;
		white-space: pre-line;
		font-size: 0.875rem;
		font-weight: bold;
		opacity: 0;
		transition: opacity 0.2s;
		pointer-events: none;
		border: 0.125rem solid var(--pitch-black);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		min-width: max-content;
	}

	.help-icon-wrapper:hover .help-tooltip,
	.help-icon-wrapper:focus .help-tooltip {
		visibility: visible;
		opacity: 1;
		pointer-events: auto;
	}

	.help-icon-wrapper:focus {
		outline: 0.125rem solid var(--sky-blue);
		outline-offset: 0.125rem;
	}
</style>
