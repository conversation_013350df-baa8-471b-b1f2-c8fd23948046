<script>
	import MidLeft from './MidVerbalLeft.svelte';
	import MidVerbalRight from './MidVerbalRight.svelte';



    /** @type {{i: any, data: any, studentAnswers: any, studentCross: any, isMarked: any, setAnswer: any, setMarked: any, setCross: any, curPassage: any, curIntro: any, curPassage_2: any}} */
    let {
        i,
        data = $bindable(),
        studentAnswers,
        studentCross,
        isMarked,
        setAnswer,
        setMarked,
        setCross,
        curPassage = $bindable(),
        curIntro = $bindable(),
        curPassage_2 = $bindable()
    } = $props();

    const isMath = false;
    const isSPR = false;

    // Annotate
    const setContent = () => {
        let intro = document.querySelector('.intro')?.innerHTML;
        let passage = document.querySelector('.passage');
        let passage2 = document.querySelector('.passage2')?.innerHTML;

        if (data.questions[i].questionType === "Student's Notes") {
            let tmpAr = passage.childNodes;
            for (let j = 0; j < tmpAr.length; j++) {
                data.questions[i].passage[j] = tmpAr[j].innerHTML;
            } 
        }
        else {
            data.questions[i].passage = passage.innerHTML;
        }

        if (data.questions[i].intro) data.questions[i].intro = intro;
        if (data.questions[i].passage2) data.questions[i].passage2 = passage2;
    }
    
    $effect(() => {
        curPassage = data.questions[i]?.passage;
        curIntro = data.questions[i]?.intro;
        curPassage_2 = data.questions[i]?.passage2;
    });


    let isEliminateTool = $state(false);

    function toggleElimination() {
        isEliminateTool = !isEliminateTool;
    }
</script>

<!-- Middle -->
{#key data.questions[i]}
    <MidLeft {data} {i} />
    <MidVerbalRight {i} {data} {studentAnswers} {studentCross} {isMarked} {setMarked} {toggleElimination} {setCross} {setAnswer} {isEliminateTool}/>
{/key}