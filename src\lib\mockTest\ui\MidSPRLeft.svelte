<script>
	import MidLeft from "./MidLeft.svelte";
</script>

<MidLeft isSPR={true}>
    <div class="spr-text">
        <h1 class="spr-header">Student-produced response directions</h1>
        <ul class="spr-list">
            <li>If you find <b>more than one correct answer</b>, enter only one answer.</li>
            <li>You can enter up to 5 characters for a <b>positive</b> answer and up to 6 characters (including the negative sign) for a <b>negative</b> answer.</li>
            <li>If your answer is a <b>fraction</b> that doesn’t fit in the provided space, enter the decimal equivalent.</li>
            <li>If your answer is a <b>decimal</b> that doesn’t fit in the provided space, enter it by truncating or rounding at the fourth digit.</li>
            <li>If your answer is a <b>mixed number</b> (such as 3 1/2), enter it as an improper fraction (7/2) or its decimal equivalent (3.5).</li>
            <li>Don’t enter <b>symbols</b> such as a percent sign, comma, or dollar sign.</li>
        </ul>
        <p class="spr-example">
            Examples
        </p>
        <div class="spr-table-left-wrapper">
            <table class="spr-table-left">
                <tbody>
                    <tr class="spr-table-left-row--1">
                        <td>Answer</td>
                        <td>Acceptable ways to enter answer</td>
                        <td>Unacceptable: will NOT receive credit</td>
                    </tr>
                    <tr class="spr-table-left-row--2">
                        <td>3.5</td>
                        <td class="spr-table-left-chivo">
                            <p>3.5</p>
                            <p>3.50</p>
                            <p>7/2</p>
                        </td>
                        <td class="spr-table-left-chivo">
                            <p>31/2</p>
                            <p>3 1/2</p>
                        </td>
                    </tr>
                    <tr class="spr-table-left-row--2">
                        <td>2/3</td>
                        <td class="spr-table-left-chivo">
                            <p>2/3</p>
                            <p>.6666</p>
                            <p>.6667</p>
                            <p>0.666</p>
                            <p>0.667</p>
                        </td>
                        <td class="spr-table-left-chivo">
                            <p>0.66</p>
                            <p>.66</p>
                            <p>0.67</p>
                            <p>.67</p>
                        </td>
                    </tr>
                    <tr>
                        <td>-1/3</td>
                        <td class="spr-table-left-chivo">
                            <p>-1/3</p>
                            <p>-.3333</p>
                            <p>-0.333</p>
                        </td>
                        <td class="spr-table-left-chivo">
                            <p>-.33</p>
                            <p>-0.33</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</MidLeft>
    

<style>
    .spr-header {
        color: #000;
        font-family: "Merriweather";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 125.5% */
    }

    .spr-list {
        margin-left: 30px;
    }

    .spr-text {
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
        padding: 32px 0 0 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
        margin-left: 32px;
    }

    .spr-example {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 27px 0 4px 0;
    }

    .spr-table-left-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;
    }

    .spr-table-left {
        width: 75%;
        border: 1px solid black;
        border-collapse: collapse;
        text-align: center;
    }

    .spr-table-left tr, .spr-table-left td {
        border: 1px solid black;
        border-collapse: collapse;
    }

    .spr-table-left-row--1 {
        width: 20%;
    }

    .spr-table-left-row--2 {
        width: 40%;
    }

    .spr-table-left td {
        padding: 23px 10px;
    }

    .spr-table-left-chivo {
        color: var(--Charcoal, #333);
        font-family: "Chivo";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 179.286% */
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>

