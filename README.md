# DSAT16

DSAT16 is a comprehensive web application designed to help students prepare for the Digital SAT. It provides mock tests, detailed performance analysis, study materials, and a vocabulary building tool.

## ✨ Key Features

-   **Mock Tests**: Simulate the real Digital SAT experience.
-   **Performance Analysis**: In-depth analysis of test results, including score predictions and breakdowns by skill.
-   **Study Resources**: Access to lectures, notes, and other study materials, managed via Contentful.
-   **Vocabulary Tool**: An AI-powered tool to help students learn and practice vocabulary.
-   **User Authentication**: Secure user accounts and progress tracking with Firebase.
-   **Subscriptions**: Paid plans for premium features, powered by Stripe.

## 🚀 Tech Stack

-   **Framework**: [SvelteKit](https://kit.svelte.dev/)
-   **Backend Services**:
    -   [Firebase](https://firebase.google.com/): Authentication, Firestore Database
    -   [Supabase](https://supabase.io/): Alternative database and backend services
    -   [Stripe](https://stripe.com/): Payments and subscriptions
    -   [Contentful](https://www.contentful.com/): Content Management System
    -   [Upstash](https://upstash.com/): Redis for caching and rate limiting
    -   [Google Generative AI](https://ai.google/): For vocabulary tool features
-   **Deployment**: Hosted on Firebase with a serverless backend.

## ⚙️ Getting Started

Follow these instructions to get the project up and running on your local machine for development and testing purposes.

### Prerequisites

-   [Node.js](https://nodejs.org/) (version 18.x or higher recommended)
-   [npm](https://www.npmjs.com/), [pnpm](https://pnpm.io/), or [yarn](https://yarnpkg.com/)

### Installation

1.  Clone the repository:
    ```bash
    git clone https://github.com/your-username/dsat16.git
    cd dsat16
    ```

2.  Install the dependencies:
    ```bash
    npm install
    # or
    pnpm install
    # or
    yarn
    ```
```

> **Note**: The client-side Firebase configuration is currently hardcoded in `src/lib/firebase/config.ts`. For a production environment, you might consider moving these to public environment variables as well.

### Running the Development Server

Once you've installed dependencies and set up your environment variables, you can start the development server:

```bash
npm run dev

# or start the server and open the app in a new browser tab
npm run dev -- --open
```

The application will be available at `http://localhost:5173`.

## 🛠️ Building for Production

To create a production version of the app:

```bash
npm run build
```

This will create a production-ready build in the `build` directory. You can preview the production build with `npm run preview`.

> To deploy your app, you will need to have an adapter configured for your target environment. This project is configured to deploy to Firebase.

## Linting and Formatting

This project uses ESLint for linting and Prettier for code formatting.

-   To check for linting issues: `npm run lint`
-   To format all files: `npm run format`
