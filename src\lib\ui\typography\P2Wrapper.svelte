<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<!-- 
    @component
    A wrapper component for P2 pargraphs on figma.
    
    Usage:
    ```tsx
    <P2Wrapper>
        <p>Lorem Ipsum</p>
    </P2Wrapper>
    ```
-->

<div class="wrapper">
    {@render children?.()}
</div>

<style>
    .wrapper {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        
        font-family: "Open Sans";
        font-size: 1.125rem;
        font-weight: 450;
        line-height: 1.6875rem;
    }

    @media (max-width: 768px) {
        .wrapper {
            font-size: 0.9375rem;
            line-height: 1.375rem;
        }
    }
</style>