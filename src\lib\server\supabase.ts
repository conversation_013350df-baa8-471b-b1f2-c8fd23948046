import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import type { Database } from '$lib/types';

/**
 * Supabase client for server-side operations
 * Uses service role key for full access.
 * * run `npx supabase gen types typescript --project-id ukdfxxqsvtulmffpppbq --schema public > src/lib/types/supabase.types.ts` to generate the types
 */
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);