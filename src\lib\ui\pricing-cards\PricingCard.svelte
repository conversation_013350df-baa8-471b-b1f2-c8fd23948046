<script lang="ts">
	import { Button, H2, H4, P2, P3 } from "$lib/ui";
    import { user } from "$lib/firebase";
	import { goto } from "$app/navigation";
	import { onMount } from "svelte";
	import { createOnetimePaymentSession } from "$lib/stripe/stripe";

    interface Props {
        label: string;
        description: string;
        price: string;
        checklist?: any;
        oldPrice?: any;
        priceId: string;
    }

    let {
        label,
        description,
        price,
        checklist = [],
        oldPrice = undefined,
        priceId
    }: Props = $props();
    let isLoading = $state(false);
    
    let isMinitest = $derived(label === "Minitest");

    let onclick: (priceId: string) => void = $state();

    let isDiscounted = $derived(oldPrice !== undefined);
    let discount = $derived(isDiscounted ? Math.round((1 - parseInt(price.slice(1)) / oldPrice.slice(1)) * 100) : 0);

    onMount(() => {
        onclick = async (priceId: string) => {
            if (isLoading) return;

            if (!$user) {
                goto("/sign-up?plan=" + label);
                return;
            }
            
            isLoading = true;

            createOnetimePaymentSession(priceId, $user.uid);
        }
    })
</script>
<div class="pricing-card">
    <div class="label">
        <P2 isBold={true}>{label.toUpperCase()}</P2>
    </div>
    <div class="description">
        <P3>{description}</P3>
    </div>
    <div class="prices">
        <div class="main-price">
            <H2>{price}</H2>
            {#if isDiscounted}
                <div class="discount">
                    <P3 isBold={true}>{discount}% off</P3>
                </div>
            {/if}
        </div>
        {#if isDiscounted}
            <div class="old-price">
                <H4 --text-color="#888888">{oldPrice}</H4>
            </div>
        {/if}
    </div>
    <hr />
    <ul class="checklist">
        {#each checklist as { text, subtext }}
            <li>
                <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15.3333 9.1569L10.8646 13.6361L9.14582 11.9173C9.05244 11.8083 8.93752 11.7197 8.80829 11.6572C8.67905 11.5947 8.53829 11.5595 8.39483 11.554C8.25137 11.5484 8.10832 11.5726 7.97465 11.625C7.84098 11.6774 7.71957 11.7568 7.61806 11.8583C7.51654 11.9598 7.43711 12.0812 7.38474 12.2149C7.33237 12.3486 7.3082 12.4916 7.31374 12.6351C7.31928 12.7785 7.35441 12.9193 7.41694 13.0485C7.47946 13.1778 7.56802 13.2927 7.67707 13.3861L10.125 15.8444C10.2223 15.9409 10.3378 16.0173 10.4647 16.0692C10.5916 16.121 10.7275 16.1473 10.8646 16.1465C11.1378 16.1453 11.3997 16.0368 11.5937 15.8444L16.8021 10.6361C16.8997 10.5392 16.9772 10.424 17.0301 10.2971C17.083 10.1701 17.1102 10.034 17.1102 9.89648C17.1102 9.75897 17.083 9.62282 17.0301 9.49588C16.9772 9.36895 16.8997 9.25374 16.8021 9.1569C16.6069 8.96289 16.3429 8.85399 16.0677 8.85399C15.7925 8.85399 15.5285 8.96289 15.3333 9.1569ZM12.5 2.08398C10.4398 2.08398 8.42581 2.69491 6.71279 3.83951C4.99978 4.98411 3.66465 6.61097 2.87624 8.51436C2.08783 10.4178 1.88154 12.5122 2.28347 14.5328C2.6854 16.5535 3.67749 18.4095 5.13429 19.8663C6.59109 21.3231 8.44716 22.3152 10.4678 22.7172C12.4884 23.1191 14.5829 22.9128 16.4863 22.1244C18.3897 21.336 20.0165 20.0009 21.1611 18.2878C22.3057 16.5748 22.9167 14.5609 22.9167 12.5007C22.9167 11.1327 22.6472 9.77817 22.1237 8.51436C21.6002 7.25056 20.833 6.10223 19.8657 5.13496C18.8984 4.16768 17.7501 3.40039 16.4863 2.87691C15.2225 2.35342 13.8679 2.08398 12.5 2.08398V2.08398ZM12.5 20.834C10.8518 20.834 9.24064 20.3452 7.87023 19.4296C6.49982 18.5139 5.43172 17.2124 4.80099 15.6897C4.17026 14.167 4.00523 12.4914 4.32677 10.8749C4.64832 9.25839 5.44199 7.77353 6.60743 6.60809C7.77287 5.44266 9.25772 4.64898 10.8742 4.32744C12.4907 4.0059 14.1663 4.17092 15.689 4.80166C17.2117 5.43239 18.5132 6.50049 19.4289 7.8709C20.3446 9.24131 20.8333 10.8525 20.8333 12.5007C20.8333 14.7108 19.9553 16.8304 18.3925 18.3932C16.8297 19.956 14.7101 20.834 12.5 20.834V20.834Z" fill="var(--icon-color, --pitch-black)"/>
                </svg>
        
                {#if subtext}
                    <div class="item-text">
                        <P2>{text}</P2>
                        <P3>{subtext}</P3>
                    </div>
                {:else}
                    <P2>{text}</P2>
                {/if}
            </li>
        {/each}
    </ul>
    <hr />
    <Button fullWidth={true} onclick={async () => await onclick(priceId)}>
        {#if isLoading}
            <img class="loading" src="/loading.gif" alt="Loading..." width="auto" />
            Redirecting...
        {:else}
            Get Started
        {/if}
    </Button>
</div>

<style>
    .pricing-card {
        background-color: white;
        box-shadow: 0.25rem 0.25rem 0 var(--box-shadow-color, var(--sky-blue));
        border: 1px solid var(--pitch-black);
        border-radius: 1rem;
        padding: 1.5rem;

        width: 100%;
        height: fit-content;
        max-width: var(--max-width, 22.5rem);

        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        gap: 2rem;
    }

    .label {
        background: var(--label-bg-color, #EEEEEE);
        color: var(--purple);
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        border: 1px solid var(--pitch-black);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        width: fit-content;
    }

    .description {
        min-height: 4.5rem;
    }

    .prices {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .main-price {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 1rem;
    }

    .discount {
        background-color: var(--light-rose);
        color: var(--rose);
        padding: 0.125rem 0.625rem;
        border-radius: 1.6875rem;
    }

    .old-price {
        text-decoration: line-through;
    }

    hr {
        height: 0.125rem;
        background-color: var(--light-sky-blue);
        border-top: 0.125rem solid var(--light-sky-blue);
    }

    .checklist {
        display: flex;
        flex-direction: column;
        gap: 0.875rem;
        list-style-type: none;
    }

    .checklist > li {
        display: inline-flex;
        gap: 0.625rem;
        align-items: flex-start;
    }

    .checklist > li > svg {
        translate: 0 2px;
        flex-shrink: 0;
    }

    .loading {
        object-fit: contain;
        width: 1.5rem;
    }

    /* .item-text {
        display: flex;
        flex-direction: column;
    } */

</style>