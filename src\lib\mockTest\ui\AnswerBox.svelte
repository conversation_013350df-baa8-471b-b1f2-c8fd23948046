<script>
	import { onMount } from "svelte";

    let { 
        studentAnswers,
        i,
        isNote = false,
        isCorrect = false
    } = $props();
    
    let input;

    let value = $derived(/\d+\/-?\d+/.test(studentAnswers[i]) ? studentAnswers[i].replace('/', '\\over') : studentAnswers[i]);
    let displayedValue = $derived(studentAnswers[i] ? '$' + value + '$' : '');

    let correct = $derived(isNote && isCorrect);

    function updateAnswer(e) {
    
        // Only allows the characters below (null is Backspace)
        if ( !['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '-', '/', null].includes(e.data)
            // Max length is 5 if no -
            || (!studentAnswers[i].startsWith('-') && studentAnswers[i].length > 5) 
            // Max length is 6 if starts with -
            || studentAnswers[i].length > 6

            // Only allows 1 character each
            || (['.', '-', '/'].includes(e.data) && studentAnswers[i].slice(0, -1).includes(e.data))

            // Only allows negative sign at the start
            || (e.data == '-' && studentAnswers[i] != '-')

            // Don't allow / at the start nor end
            || (e.data == '/' && studentAnswers[i] == '/')
            || (e.data == '/' && studentAnswers[i][4] == '/' && studentAnswers[i].length == 5)
            || (e.data == '/' && studentAnswers[i][5] == '/' && studentAnswers[i].length == 6)
        ) {
            studentAnswers[i] = studentAnswers[i].slice(0, -1);
        }        
    }

    onMount(() => {
        if (!isNote) input.focus({
            preventScroll: true
    })})

    $effect(() => {
        displayedValue;
        try {
            if (!isNote) {
                MathJax.typeset();
            } else {
                window.renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                    ],
                    
                    throwOnError : false
                })
            }
        } catch (error) {}
    })
</script>

<!-- Answer box -->
<div class="answer-box-container" class:answer-box-container--note={isNote}>
    <div class="answer-box" class:answer-box--long={studentAnswers[i] && studentAnswers[i].startsWith('-')} class:answer-box--correct={correct}>
        <input class="answer-input" type="text" bind:this={input} bind:value={studentAnswers[i]} oninput={updateAnswer} class:answer-input--long={studentAnswers[i] && studentAnswers[i].startsWith('-')}>
    </div>
</div>

<!-- Answer Preview -->
<div class="answer-preview-container" class:answer-preview-container--note={isNote}>
    {#key displayedValue}
        <div class="answer-preview">
            Answer Preview: {displayedValue}
        </div>                    
    {/key}
</div>


<style>
    
    .answer-box-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding-top: 20px;
        width: 100%;
    }

    .answer-box-container--note {
        padding-top: 0;
    }

    .answer-box {
        width: 105px;
        height: 57px;
        border-radius: 8px;
        border: 1px solid #333333;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .answer-box--long {
        width: 124px;
    }

    .answer-box--correct {
        background-color: #D1FFEE;
    }

    .answer-input {
        width: 90px;
        bottom: 8px;
        border: 0;
        outline: 0;
        background: transparent;
        border-bottom: 1px solid #333333;
        position: absolute;
        color: #505050;
        font-family: "Chivo";
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 89.643% */
    }

    .answer-input--long {
        width: 105px;
    } 

    .answer-preview-container {
        display: flex;
        padding-top: 40px;
        align-items: center;
        justify-content: flex-start;
        gap: 12px;
        width: 100%;
        margin-bottom: 32px;
    }

    .answer-preview-container--note {
        padding-top: 8px;
        margin-bottom: 0;
    }

    .answer-preview {
        color: #000;
        font-family: "Merriweather";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 125.5% */
    }
</style>