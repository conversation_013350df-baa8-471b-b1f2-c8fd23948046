<script>
	import { H1, H2, P1, P1<PERSON><PERSON><PERSON> } from "$lib/ui";
    import stepOne from "$lib/assets/step-1.png?enhanced";
    import stepTwo from "$lib/assets/step-2.png?enhanced";
</script>

<svelte:head>
    <title>One Last Step</title>
    <meta name="robots" content="noindex">
</svelte:head>

<div class="container">
    <P1Wrapper>
        <p>You're almost done!</p>
        <p>Before you get access to free updates, SAT tips and strategies, <b>you need to confirm your email right now.</b></p>
        <p>(It's simple)</p>
        <p><b>Step 1:</b> Check your inbox for an email from me with the subject line: "Please confirm your subscription"</p>
        <enhanced:img src={stepOne} alt="Email Confirmation Step 1" width=100% />
        <p>Gmail users: If you can't find it, check your <b>Promotions</b> tab or <b>Spam</b>.</p>
        <p><b>Step 2:</b> Click on that huge orange button in the email to confirm your subscription.</p>
        <enhanced:img src={stepTwo} alt="Email Confirmation Step 2" width=100% />
        <p>That's it! You're all set.</p>
        <p>Once you confirm, you'll get access to free SAT tips and strategies.</p>
    </P1Wrapper>
</div>

<style>
    .container {
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        gap: 4em;
        padding: 4em 1.25em 9em 1.25em;
        max-width: 50rem;
    }
</style>