import { getAuth, onAuthStateChanged, type User } from "firebase/auth";
import { writable, type Readable } from "svelte/store";
import { app } from "./config";

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

function userStore(): Readable<User | null> {
  let unsubscribe: () => void;

  if (!auth || !globalThis.window) {
    const { subscribe } = writable<User | null>(null);
    return {
      subscribe,
    }
  }

  const { subscribe } = writable<User | null>(auth?.currentUser ?? null, (set) => {
    unsubscribe = onAuthStateChanged(auth, (user) => {
      set(user);
    });

    return () => unsubscribe();
  });

  return {
    subscribe,
  };
}

export const user = userStore();

