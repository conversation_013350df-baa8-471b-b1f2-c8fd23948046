<script>
    /** @type {{test: any, student: any, i: any, m?: any}} */
    let {
        test,
        student,
        i,
        m = -1
    } = $props();
    const letters = ['A', 'B', 'C', 'D'];

    const PRIOR = [0, 27, 54, 76];
    let numberOfPriorQuestions = $derived(PRIOR[m] ?? 0);

    let correctAnswer = $derived(test.questions[i].correctAnswer);
    let studentsAnswer = $derived(student.answers[i + numberOfPriorQuestions]);

    function isIndex(answer, index) {
        return answer === index;
    }
</script>

<div class="choices">
    {#each test.questions[i].choices as choice, index}
        <div class="answer-container">
            <button class="answer-choice" class:answer-correct={isIndex(correctAnswer, index)} class:answer-incorrect={!isIndex(correctAnswer, index) && isIndex(studentsAnswer, index)}>
                <div class="answer-letter-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" viewBox="0 0 27 27">
                        <circle cx="13.5" cy="13.5" r="12.5" fill={isIndex(correctAnswer, index) ? "#42FFB7" : isIndex(studentsAnswer, index) ? "#FF66C4" : "#FFFFFF"} stroke="#333333"/>
                    </svg>
                    <span class="answer-letter">{letters[index]}</span>
                </div>

                {#if Array.isArray(choice)}
                    <div class="choice-table-wrapper">
                        <table class="table table--choice">
                            <tbody>
                            {#each choice as row }
                                <tr>
                                    {#each row as item }
                                        <td>{@html item}</td>
                                    {/each}
                                </tr>
                            {/each}
                            </tbody>
                        </table>
                    </div>
                {:else}
                    <span class="answer-text">{@html choice}</span>
                {/if}

            </button>
        </div>
    {/each}
</div>

<style>
    button {
        border: 0;
        background: none;
    }

    button:hover {
        opacity: 1 !important;
    }

    .answer-choice {
        display: inline-flex;
        min-height: 50px;
        width: 100%;
        max-width: 700px;
        padding: 12px 13px;
        align-items: center;
        gap: 21px;
        border-radius: 8px;
        border: 1px solid var(--charcoal, #333);
    }

    .answer-container {
        margin-top: 16px;
    }

    .answer-correct {
        background-color: var(--web-color-light-aquamarine, #D1FFEE);
    }

    .answer-incorrect {
        background-color: var(--web-color-light-rose, #FFDAF1);
    }

    .answer-letter-container {
        display: flex;
        align-items: center;
        position: relative;
        stroke-width: 2px;
        stroke: var(--charcoal, #333);
    }

    .answer-letter {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        color: var(--charcoal, #333);
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .answer-text {
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
        text-align: start;
    }   
    
    .table {
        width: 100%;
        text-align: center;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table tr {
        width: 50%;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table td {
        padding: 14px 10px;
        border: 1px solid black;
        border-collapse: collapse;
        color: var(--Charcoal, #333);

        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .choice-table-wrapper {
        margin: 12px 0;
    }

    .table--choice td {
        padding: 13px 22px;
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>