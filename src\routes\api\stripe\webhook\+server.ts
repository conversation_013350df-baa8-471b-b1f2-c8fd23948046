import Stripe from "stripe";
import { PRIVATE_STRIPE_KEY, PRIVATE_STRIPE_KEY_TEST, STRIPE_WEBHOOK, STRIPE_WEBHOOK_LOCAL } from "$env/static/private";
import { json } from "@sveltejs/kit";
import { adminDB } from "$lib/server/admin.ts";
import { dev } from "$app/environment";

const stripe = new Stripe(dev ? PRIVATE_STRIPE_KEY_TEST  : PRIVATE_STRIPE_KEY);

export const POST = async ({ request }) => {
    let eventType: Stripe.Event['type'];
    let data: Stripe.Event['data']; 

	if (dev ? STRIPE_WEBHOOK_LOCAL : STRIPE_WEBHOOK) {
		// SvelteKit may sometimes modify the incoming request body
		// However, Stripe requires the exact body it sends to construct an Event
		// To avoid unintended SvelteKit modifications, we can use this workaround:
		const _payload = await request.arrayBuffer();
		const payload = Buffer.from(_payload);

		const signature = request.headers.get('stripe-signature');
		try {
			const event = stripe.webhooks.constructEvent(payload, signature, (dev ? STRIPE_WEBHOOK_LOCAL : STRIPE_WEBHOOK) );
            
			data = event.data;
			eventType = event.type;
		} catch (err) {
			console.error(err);
            return json({ error: err }, { status: 500 });
		}
	} else {
		const formData = await request.formData();
		data = JSON.parse(formData.get('data') as string);
		eventType = formData.get('type') as Stripe.Event['type'];
	}

	switch (eventType) {
		case 'checkout.session.completed': {
			const sessionData = data.object as Stripe.Checkout.Session;
			// Retrieve the checkout session with expanded line items
			const session = await stripe.checkout.sessions.retrieve(
				sessionData.id,
				{
					expand: ["line_items"],
				}
			);

			const products = await getProductsFromLineItems(session.line_items.data);
			const highestRole = getHighestRole(products);
			await updateUserRole(sessionData.customer as string, highestRole, products);
			
			console.log('Event: checkout.session.completed');
			break;
		}

		case 'customer.subscription.created':
		case 'customer.subscription.deleted':
		case 'customer.subscription.paused':
		case 'customer.subscription.updated': {
			const subscriptionData = data.object as Stripe.Subscription;
			await handleSubscriptionChange(subscriptionData.customer as string);
			console.log(`Event: ${eventType}`);
			break;
		}

		default:
		// Unhandled event type
	}

    return json({ message: 'Success' }, { status: 200 });
};

// Define role hierarchy
const ROLE_HIERARCHY = {
    'Free': 0,
    'Question Bank': 1,
    'Bootcamp': 1,
    'Pro': 2
};

// Helper function to get highest role from purchased products
function getHighestRole(products: Stripe.Product[]): string {
    let highestRole = 'Free';
    let highestLevel = ROLE_HIERARCHY['Free'];
    let hasQuestionBank = false;
    let hasBootcamp = false;

    for (const product of products) {
        const role = product.metadata.role;
        if (role) {
            if (role === 'Question Bank') hasQuestionBank = true;
            if (role === 'Bootcamp') hasBootcamp = true;
            if (ROLE_HIERARCHY[role] > highestLevel) {
                highestRole = role;
                highestLevel = ROLE_HIERARCHY[role];
            }
        }
    }

    // If user has both Question Bank and Bootcamp, upgrade to Pro
    if (hasQuestionBank && hasBootcamp) {
        return 'Pro';
    }

    return highestRole;
}

// Helper function to update user role
async function updateUserRole(stripeId: string, role: string, currentProducts?: Stripe.Product[]) {
    if (dev) console.log(`Updating user ${stripeId} to role ${role}`);

    const userSnapshot = await adminDB.collection('users').where('stripeId', '==', stripeId).get();
    if (userSnapshot.empty) {
        console.error(`No user found with stripeId: ${stripeId}`);
        return;
    }

    const userRef = userSnapshot.docs[0].ref;
    const userData = userSnapshot.docs[0].data();
    const currentRole = userData.role;

    // Special case: If user is Pro and losing Question Bank, downgrade to Bootcamp
    if (currentRole === 'Pro' && currentProducts) {
        const hasQuestionBank = currentProducts.some(product => product.metadata.role === 'Question Bank');
        const hasBootcamp = currentProducts.some(product => product.metadata.role === 'Bootcamp');
        
        if (!hasQuestionBank && hasBootcamp) {
            await userRef.update({ role: 'Bootcamp' });
            return;
        }
    }

    await userRef.update({ role });
}

// Helper function to handle subscription changes
async function handleSubscriptionChange(customerId: string) {
    const subscriptions = await stripe.subscriptions.list({
        customer: customerId,
        status: 'active'
    });

    if (subscriptions.data.length === 0) {
        await updateUserRole(customerId, 'Free');
        return;
    }

    const products = await getProductsFromSubscriptions(subscriptions.data);
    const newRole = getHighestRole(products);
    await updateUserRole(customerId, newRole, products);
}

// Helper function to get products from line items
async function getProductsFromLineItems(lineItems: Stripe.LineItem[]): Promise<Stripe.Product[]> {
    const products: Stripe.Product[] = [];
    
    for (const item of lineItems) {
        const price = item.price;
        if (typeof price.product === 'string') {
            const product = await stripe.products.retrieve(price.product);
            if ('active' in product) {
                products.push(product);
            }
        } else if ('active' in price.product) {
            products.push(price.product);
        }
    }
    
    return products;
}

// Helper function to get products from subscriptions
async function getProductsFromSubscriptions(subscriptions: Stripe.Subscription[]): Promise<Stripe.Product[]> {
    const products: Stripe.Product[] = [];
    
    for (const sub of subscriptions) {
        for (const item of sub.items.data) {
            const price = item.price;
            if (typeof price.product === 'string') {
                const product = await stripe.products.retrieve(price.product);
                if ('active' in product) {
                    products.push(product);
                }
            } else if ('active' in price.product) {
                products.push(price.product);
            }
        }
    }
    
    return products;
}
