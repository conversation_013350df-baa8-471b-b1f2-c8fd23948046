<script>
    import Passage from "$lib/mockTest/ui/Passage.svelte";
	import Graph from "$lib/mockTest/ui/Graph.svelte";
	import Intro from "$lib/mockTest/ui/Intro.svelte";
	import MidLeft from "./MidLeft.svelte";
    
    /** @type {{i: any, data: any, child?: import('svelte').Snippet}} */
    let { i, data, children } = $props();
    const isWalkthrough = false;

    const isSPR = false;
    const isMath = false;
</script>

{#snippet explanation()}
    {@render children?.()}
{/snippet}

<MidLeft {isSPR} {isWalkthrough}>
    {#snippet children()}
    
            {#if data.questions[i].graph}
                <Graph graph={data.questions[i].graph} {isMath}/>
            {/if}
            
            {#if data.questions[i].intro}
                <Intro intro={data.questions[i].intro}></Intro>
            {/if}
        
            {#if data.questions[i].passage2}
                <div class="paired-text">Text 1</div>
            {/if}
        
            <!-- If the question is of type Student's notes -->
            {#if Array.isArray(data.questions[i].passage)}
        
                <ul class="qn-list fiction passage">
                    {#each data.questions[i].passage as point}
                        <li>{@html point}</li>            
                    {/each}
                </ul>
        
            <!-- Every other normal questions -->
            {:else}
                <Passage intro={data.questions[i].intro} passage={data.questions[i].passage} />
            {/if}
        
            <!-- If paired passage -->
            {#if data.questions[i].passage2}
                <div class="paired-text text-2">Text 2</div>
            {/if}
        
            {#if data.questions[i].passage2}
                <Passage passage={data.questions[i].passage2} />
            {/if}
            
            {@render explanation()}
    {/snippet}
</MidLeft>


<style>
    .paired-text {
        color: #000;
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 156.875% */
        margin-bottom: 6px;
    }

    .text-2 {
        margin-top: 38px;
    }

    .passage {
        max-width: 645px;   
        width: 100%;
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .qn-list {
        margin-left: 1.8em;
        padding-left: 0;
        max-width: 645px;   
        color: var(--charcoal, #333);
        width: auto;
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .fiction {
        padding: 10px 0 0 20px;
    }
</style>