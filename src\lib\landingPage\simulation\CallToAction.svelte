<script>

	import { H3 } from "$lib/ui";
    import SectionWrap<PERSON> from "../SectionWrapper.svelte";

</script>
<SectionWrapper --bg-color=var(--sky-blue) --padding-top=8.4375rem --padding-bottom=8.4375rem>
    <div class="call-to-action">
        <div class="left">
            <H3>Stop doing random questions hoping your score will magically increase.</H3>
        </div>

        <div class="right">
            <H3>Start following a system to activate your improvement cycle.</H3>
        </div>
    </div>
</SectionWrapper>

<style>
    .call-to-action {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-self: center;
        align-items: center;
        gap: 4rem;
        width: 100%;
        max-width: 70rem;
        padding: 1.25rem;
    }

    .left, .right {
        padding: 2rem;
        border: 0.25rem solid var(--pitch-black);
        box-shadow: 1.25rem 1.25rem 0 var(--pitch-black);
    }

    .left {
        max-width: 22rem;
        background-color: var(--yellow);
        text-wrap: balance;
    }

    .right {
        max-width: 27rem;
        background-color: var(--light-yellow);
        text-wrap: balance;
    }

    @media (max-width: 768px) {
        .call-to-action {
            flex-direction: column;
            gap: 8rem;
        }

        .left, .right {
            margin-left: -1.25rem;
        }
    }
</style>