<!-- 
    @component
    ## MinitestIntro
    The introduction to the minitest.
-->

<script>
    import { Check<PERSON>, Slider } from "$lib/ui";
    import { updateDoc, doc } from 'firebase/firestore';
    import { db } from '$lib/firebase';

    /** @type {{uid: any}} */
    let { uid } = $props();

    let noAim = $state(false);
    let noPrevious = $state(false);
    
    let aimScore = $state(1500);
    let verbalScore = $state(500);
    let mathScore = $state(500);
    
    let currentScore = $derived(verbalScore + mathScore);

    export const saveScores = async () => {
        const docRef = doc(db, 'users', uid);

        // Update the user's aim score and current score
        await updateDoc(docRef, {
            aimScore: !noAim ? aimScore : null,
            previousVerbalScore: !noPrevious ? verbalScore : null,
            previousMathScore: !noPrevious ? mathScore : null
        });

        // Cache the user's aim score and current score
        localStorage.setItem('aimScore', !noAim ? aimScore : null);
        localStorage.setItem('previousVerbalScore', !noPrevious ? verbalScore : null);
        localStorage.setItem('previousMathScore', !noPrevious ? mathScore : null);
    }
</script>

<div class="minitest-intro-wrapper">
    <div class="minitest-intro">
        <div class="intro-title">DSAT16 Simulation</div>
        <div class="intro">
            <div class="intro-text">
                <p>Before you start, we would like to ask you some questions regarding your plan and experience with the Digital SAT.</p>
                <p>Your answer will be taken into consideration to deliver a comprehensive analysis of your result and a personalized study plan.</p>    
            </div>
            <div class="intro-questions">
                <Slider label={"What is your target SAT score?"} min={400} max={1600} step={10} bind:value={aimScore}/>
                <Checkbox label={"I just want to improve my score as much as possible."} bind:isChecked={noAim}/>
                
            </div>
            <div class="intro-questions">
                <Slider label={"What is your current SAT score?"} min={400} max={1600} step={10} value={currentScore} disabled={true}/>
                <Slider label={"Verbal"} min={200} max={800} step={10} bind:value={verbalScore} isRightAligned={true}/>
                <Slider label={"Math"} min={200} max={800} step={10} bind:value={mathScore} isRightAligned={true}/>
                <Checkbox label={"I have never taken an SAT test or mock test before."} bind:isChecked={noPrevious}/>
            </div>
            

        </div>
    </div>
</div>

<style>
    .minitest-intro-wrapper {
        display: flex;
        justify-content: center;
        height: calc(100vh - 180px);
        font-family: "Inter";
        overflow: auto;
    }

    .minitest-intro {
        display: flex;
        flex-direction: column;
        gap: 33px;
        width: 60%;
        padding-top: 64px;
    }

    .intro-title {
        text-align: center;
        font-size: 32px;
    }

    .intro {
        display: flex;
        flex-direction: column;
        gap: 1.375rem;
        font-size: 16px;
    }

    .intro-text {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .intro-questions {
        display: flex;
        flex-direction: column;
        gap: 0.375rem;
        width: 100%;
        justify-content: flex-end;
        margin-bottom: 32px;
    }
</style>