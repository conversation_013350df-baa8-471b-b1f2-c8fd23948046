<script>
	import { P3 } from "$lib/ui";
	import H5 from "$lib/ui/typography/H5.svelte";
	import QuestionTypeProgess from "./QuestionTypeProgess.svelte";
 

    /** @type {{studentPerformances: any, isVerbal?: boolean}} */
    let { studentPerformances, isVerbal = true } = $props();

    let current = $state(0);

    function setCurrent(i) {
        current = i;
    }

    const DEFINITION = {
        "Student's Notes": "The question lists 4-8 bullet points, and you must choose one to illustrate the student's topic.",
        "Transitions": "Questions that require you to fill in the blank with the most logical transition",
        "Punctuations": "You will be asked to choose among 4 answer choices that are similar except for the punctuation marks they use.",
        "Grammar": "You need to fill in the blank with a grammatically correct choice.",
        "Words in Context": "These questions ask you to use the most appropriate one to fill in the blank or choose the one with the closest meaning to another word.",
        "Reading Comprehension": "These questions ask you about information that are already stated in the passage.",
        "Critical Reading": "These questions give you a long passage and expect you to draw new information in order to solve it.",
        "Synthesis Reading": "These have 2 sources of the information instead of one (a passage and a graph or two passages)",
        "Algebra": "These Math questions mainly concern with equations, inequations, functions, and graphs.",
        "Data Analysis": "Concepts such as probability, statistics, and data modelling will appear in these questions.",
        "Geometry": "You will be tested on common 2D and 3D shapes, as well as some other topics such as trigonometry."
    };
</script>
<div class="wrapper">
    <div class="container" style="--light-color: var({isVerbal ? "--light-sky-blue" : "--light-rose"}); --color: var({isVerbal ? "--sky-blue" : "--rose"})">
        <div class="skill-container">
            {#each studentPerformances as s, i}
            <button onclick={() => setCurrent(i)}>
                <QuestionTypeProgess score={s.score} count={s.count} {isVerbal} isFirstChild={i == 0} isLastChild={i == studentPerformances.length - 1} questionType={s.questionType} isChosen={i === current} />
            </button>
            {/each}
        </div>
        <div class="assessment-container">
            <div class="assessment">
                <div class="heading">
                    <P3 --pitch-black="#888888">Question Type</P3>
                    <H5>{studentPerformances[current].questionType}</H5>
                </div>
                <P3>You got <span>{studentPerformances[current].score}/{studentPerformances[current].count}</span> questions correct</P3>
                <P3 --pitch-black="#777777">{DEFINITION[studentPerformances[current].questionType]}</P3>
                <P3 --pitch-black="#777777">Tips: {studentPerformances[current].tips}</P3>
            </div>
        </div>
    </div>
</div>


<style>
    .wrapper {
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        border-radius: 0.75rem;
        border: 1px solid var(--pitch-black);
        padding: 0.9375rem;
        background-color: var(--primary-color, var(--light-tangerine));
    }

    .container {
        display: inline-flex;
        border-radius: 0.75rem;
        width: 100%;
        min-height: 382.4px;
    }

    .skill-container {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    button {
        text-align: start;
    }

    .assessment-container {
        padding: 1.25rem 1.875rem 0 1.875rem;
        display: flex;
        justify-content: center;
        align-items: start;
        border-radius: 0 0.75rem 0.75rem 0;
        background-color: var(--secondary-color, var(--light-yellow));
        width: 50%;
        border: 1px solid var(--pitch-black);
    }

    .assessment {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        align-items: center;
        justify-content: center;
        text-align: center;
        text-wrap: balance;
        text-wrap: pretty;
    }

    .heading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
</style>