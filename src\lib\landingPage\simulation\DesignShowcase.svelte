<script>
	import { H2, P1, P2 } from "$lib/ui";
	import DotCard from "./DotCard.svelte";
    import SectionWrapper from "../SectionWrapper.svelte";

    import testInterface from "$lib/assets/designShowcase/test_interface.webp?enhanced&w=720;500";
    import result from "$lib/assets/designShowcase/result.webp?enhanced&w=720;500";
    import answerPlusExplanation from "$lib/assets/designShowcase/answer_plus_explanation.webp?enhanced&w=720;500";
    import analysis from "$lib/assets/designShowcase/analysis.webp?enhanced&w=720;500";

</script>
<SectionWrapper --bg-color=var(--light-tangerine) --padding-top=4.25rem --padding-bottom=11.75rem>
    <div class="wrapper">
        <div class="bonus-section" role="region" aria-labelledby="bonus-title">
            <div class="bonus-banner" role="banner">
                <P2 isBold=true><span class="bonus-text">✨BONUS✨</span></P2>
            </div>
            
            <H2>The beauty in our design will motivate you to study more</H2>
            
            <P1>For sure 😏 Seriously, just look at these 👇</P1>
        </div>
        <div class="card-container">
            <DotCard title="Test Interface" image={testInterface} />
            <DotCard title="Result" image={result}/>
            <DotCard title="Answer + Explanation" image={answerPlusExplanation}/>
            <DotCard title="Analysis" image={analysis}/>
        </div>
    </div>
</SectionWrapper>

<style>
    .wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 6.25rem;
        width: 100%;
    }

    .bonus-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.625rem;
        max-width: 32.5rem;
        width: 100%;
        text-align: center;
        text-wrap: balance;
        text-wrap: pretty;
    }

    .bonus-banner {
        background-color: white;
        color: var(--rose);
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        border: 1px solid var(--pitch-black);
        box-shadow: 0.25rem 0.25rem 0 0 var(--pitch-black);
        width: fit-content;
    }

    .card-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rem;
        padding-right: 1.25rem;
    }

    @media (max-width: 768px) {
        .wrapper {
            gap: 3rem;
        }

        .card-container {
            gap: 4rem;
            padding-right: 0.25rem;
        }
    }
</style>