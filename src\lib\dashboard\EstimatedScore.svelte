<script>
    import H4 from '$lib/ui/typography/H4.svelte';
    import H5 from '$lib/ui/typography/H5.svelte';
    import ScoreDescription from './ScoreDescription.svelte';
</script>

<H4>Your Estimate Score</H4>
<div class="main-score-container flex flex-col items-center w-fit">
    <div class="grid score-wrapper">
        <p class="col-start-1 row-start-1 score-shadow w-fit">1320</p>
        <p class="col-start-1 row-start-1 score w-fit">1320</p>
    </div>
    <ScoreDescription isPositive>40</ScoreDescription>
    <p class="score-description-text">
        since last simulation
    </p>
</div>
<div class="flex flex-col w-fit gap-2">
    <div class="flex flex-row w-fit gap-4">
        <p class="score-component" style:color={"#66E2FF"}>640</p>
        <div class="flex flex-col">
            <H5>Reading and Writing</H5>
            <ScoreDescription isPositive>60</ScoreDescription>
        </div>
    </div>
    <div class="flex flex-row w-fit gap-4">
        <p class="score-component" style:color={"#FF66C4"}>680</p>
        <div class="flex flex-col">
            <H5>Math</H5>
            <ScoreDescription>20</ScoreDescription>
        </div>
    </div>
</div>

<style>
    * {
        font-family: 'Inter';
    }

    .score, .score-shadow {
        font-family: 'Inter';
        font-size: 90px;
        font-style: normal;
        font-weight: 900;
        line-height: normal;
        background: linear-gradient(79deg, #66E2FF 3.21%, #66E2FF 33.91%, #F075C0 65.85%, #F075C0 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .score-shadow {
        text-shadow: 4px 4px 0px #000;
        -webkit-text-stroke-width: 2px;
        -webkit-text-stroke-color: #000;
    }

    .score-component {
        font-family: 'Inter';
        font-size: 45px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
    }

</style>

