<!-- 
    @component
    P1 pargraphs on figma.
    
    Usage:
    ```tsx
    <P1>Lorem Ipsum</P1>
    ```
-->
<script>
    /** @type {{isBold?: boolean, children?: import('svelte').Snippet}} */
    let { isBold = false, children } = $props();
</script>

<p class:bold={isBold}>
    {@render children?.()}
</p>

<style>
    p { 
        font-family: "Open Sans";
        font-size: 1.25rem;
        font-weight: 450;
        line-height: 1.875rem;
        color: var(--text-color, --pitch-black);
        text-decoration-color: var(--text-color, --pitch-black);
    }
    
    .bold {
        font-weight: 600;
        font-family: "Inter";
    }   

    @media (max-width: 540px) {
        p {
            font-size: 1rem;
            line-height: 1.5rem;
        }
    }
</style>