import { adminDB } from '$lib/server/admin.ts'
import { error } from '@sveltejs/kit';
import { client } from '$lib/server/contentful.ts';

export async function load({ locals }) {
    const progresses = [];
    
    // This takes a long time to load
    let docs = await adminDB.collection("Simulation")
        .where("user", "==", locals.uid)
        .orderBy("predictedTotalScore", "desc")
        .get();

    docs.forEach(doc => {
        progresses.push({
            id: doc.id,
            ...doc.data()
        })
    })  

    let data = await client.getEntries({
        content_type: 'simulation',
        order: ['fields.slug'],
        include: 1
    }).catch((e) => error(404, e.message));

    if (data.total === 0) error(404);

    let simulations = data.items.map((item) => item.fields);

    // Get the progress of each simulation
    const simulationDetails = [];

    const i = 2;

    let progress = progresses.find((progress) => progress.simulation == simulations[i].slug);
    simulationDetails.push({
        title: simulations[i].title,
        slug: simulations[i].slug,
        progress: {
            id: progress?.id,
            score: progress?.predictedTotalScore,
        }
    })

    return { simulationDetails };
}
