<script>
	import { page } from "$app/stores";
    import { <PERSON><PERSON>, P2 } from "$lib/ui";

    let id = $page.url.pathname.split('/').at(-1);
    let isMinitest = $derived($page.url.pathname.startsWith("/minitest"));
    let shareUrl = $derived(isMinitest ? $page.url : `dsat16.com/share-analysis/${id}`);

    let linkCopied = $state(false);
    const copyToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(shareUrl);
            linkCopied = true;
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    }
</script>

<div class="share-link-container">
    <div class="link-display">
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.1098 15.8901L8.22984 19.7701C7.76007 20.2235 7.1327 20.4768 6.47984 20.4768C5.82698 20.4768 5.1996 20.2235 4.72984 19.7701C4.49944 19.5406 4.31663 19.2679 4.19189 18.9676C4.06715 18.6673 4.00294 18.3453 4.00294 18.0201C4.00294 17.6949 4.06715 17.3729 4.19189 17.0726C4.31663 16.7723 4.49944 16.4996 4.72984 16.2701L8.60984 12.3901C8.79814 12.2018 8.90393 11.9464 8.90393 11.6801C8.90393 11.4138 8.79814 11.1584 8.60984 10.9701C8.42153 10.7818 8.16614 10.676 7.89984 10.676C7.63353 10.676 7.37814 10.7818 7.18984 10.9701L3.30984 14.8601C2.52819 15.7109 2.10546 16.8308 2.12991 17.9858C2.15436 19.1409 2.62412 20.2419 3.44107 21.0589C4.25802 21.8758 5.359 22.3456 6.51408 22.37C7.66917 22.3945 8.78904 21.9717 9.63984 21.1901L13.5298 17.3101C13.7181 17.1218 13.8239 16.8664 13.8239 16.6001C13.8239 16.3338 13.7181 16.0784 13.5298 15.8901C13.3415 15.7018 13.0861 15.596 12.8198 15.596C12.5535 15.596 12.2981 15.7018 12.1098 15.8901ZM20.6898 3.81009C19.8486 2.9741 18.7108 2.50488 17.5248 2.50488C16.3389 2.50488 15.2011 2.9741 14.3598 3.81009L10.4698 7.69009C10.3766 7.78333 10.3026 7.89402 10.2522 8.01585C10.2017 8.13767 10.1757 8.26824 10.1757 8.40009C10.1757 8.53195 10.2017 8.66252 10.2522 8.78434C10.3026 8.90617 10.3766 9.01686 10.4698 9.11009C10.5631 9.20333 10.6738 9.27729 10.7956 9.32775C10.9174 9.37821 11.048 9.40419 11.1798 9.40419C11.3117 9.40419 11.4423 9.37821 11.5641 9.32775C11.6859 9.27729 11.7966 9.20333 11.8898 9.11009L15.7698 5.23009C16.2396 4.77672 16.867 4.52335 17.5198 4.52335C18.1727 4.52335 18.8001 4.77672 19.2698 5.23009C19.5002 5.45958 19.683 5.7323 19.8078 6.03261C19.9325 6.33292 19.9967 6.65491 19.9967 6.98009C19.9967 7.30528 19.9325 7.62727 19.8078 7.92758C19.683 8.22789 19.5002 8.50061 19.2698 8.73009L15.3898 12.6101C15.2961 12.7031 15.2217 12.8137 15.1709 12.9355C15.1202 13.0574 15.094 13.1881 15.094 13.3201C15.094 13.4521 15.1202 13.5828 15.1709 13.7047C15.2217 13.8265 15.2961 13.9371 15.3898 14.0301C15.4828 14.1238 15.5934 14.1982 15.7153 14.249C15.8371 14.2998 15.9678 14.3259 16.0998 14.3259C16.2318 14.3259 16.3626 14.2998 16.4844 14.249C16.6063 14.1982 16.7169 14.1238 16.8098 14.0301L20.6898 10.1401C21.5258 9.29887 21.995 8.16107 21.995 6.97509C21.995 5.78912 21.5258 4.65131 20.6898 3.81009ZM8.82984 15.6701C8.92328 15.7628 9.03409 15.8361 9.15593 15.8859C9.27777 15.9356 9.40823 15.9609 9.53984 15.9601C9.67144 15.9609 9.80191 15.9356 9.92374 15.8859C10.0456 15.8361 10.1564 15.7628 10.2498 15.6701L15.1698 10.7501C15.3581 10.5618 15.4639 10.3064 15.4639 10.0401C15.4639 9.77379 15.3581 9.5184 15.1698 9.33009C14.9815 9.14179 14.7261 9.036 14.4598 9.036C14.1935 9.036 13.9381 9.14179 13.7498 9.33009L8.82984 14.2501C8.73611 14.3431 8.66171 14.4537 8.61095 14.5755C8.56018 14.6974 8.53404 14.8281 8.53404 14.9601C8.53404 15.0921 8.56018 15.2228 8.61095 15.3447C8.66171 15.4665 8.73611 15.5771 8.82984 15.6701Z" fill="#777777"/>
        </svg>    
        <P2 --text-color="#777777" isEllipsis={true}>{shareUrl}</P2>
    </div>
    <Button onclick={copyToClipboard}>
        {linkCopied ? "Copied!" : "Copy"}
    </Button>
</div>

<style>
    .share-link-container {
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        width: 100%;
        max-width: 50rem;
    }

    .link-display {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        border: 1px solid #777777;
        border-radius: 0.375rem;
        padding: 0.625rem;
        flex: 1 1 0;
        overflow: hidden;
        background-color: white;
    }

    .copy-button {
        border-radius: 2.1875rem;
        border: 1px solid var(--pitch-black);
        padding: 0.75rem 1.6875rem;
        background-color: var(--sky-blue);
        font-size: 0.9375rem;
        font-weight: 600;
        font-family: "Inter";
    }
</style>