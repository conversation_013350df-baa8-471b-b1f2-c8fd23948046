<script lang="ts">
    let { value, correctAnswer } = $props();

    let isNegative = $derived(value?.startsWith?.('-'));
    let isFractionCorrect = $derived(value?.includes?.('/') && (value?.split?.('/')?.[0] / value?.split?.('/')?.[1]));

    function isCorrect() {
        return value && correctAnswer.recommended.concat(correctAnswer.possible).some(answer => value == answer || isFractionCorrect == answer )
    }
</script>

<!-- Answer box -->
<div class="answer-box-container">
    <div class="answer-box {isCorrect() ? "answer-box--correct" : "answer-box--incorrect"}" class:answer-box--long={isNegative}>
        <span class="answer-input" class:answer-input--long={isNegative}>
            {value ?? ""}
        </span>
    </div>
</div>

<!-- Answer Preview -->
{#key correctAnswer}
<div class="correct-answer-container">
        <div class="correct-answer">
            Correct Answer: ${correctAnswer.recommended.join("$ or $")}$
        </div>                    
</div>
{/key}


<style>
    
    .answer-box-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding-top: 20px;
        width: 100%;
    }

    .answer-box--correct {
        background: var(--Web-color-Light-Aquamarine, #D1FFEE);
    }

    .answer-box--incorrect {
        background: var(--Web-color-Light-Rose, #FFDAF1);
    }

    .answer-box {
        width: 105px;
        height: 57px;
        border-radius: 8px;
        border: 1px solid #333333;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .answer-box--long {
        width: 124px;
    }

    .answer-input {
        width: 90px;
        bottom: 8px;
        border: 0;
        outline: 0;
        background: transparent;
        border-bottom: 1px solid #333333;
        position: absolute;
        color: #505050;
        font-family: "Chivo";
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 89.643% */
    }

    .answer-input--long {
        width: 105px;
    } 

    .correct-answer-container {
        display: flex;
        padding-top: 40px;
        align-items: center;
        justify-content: flex-start;
        gap: 12px;
        width: 100%;
    }

    .correct-answer {
        color: #000;
        font-family: "Merriweather";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 125.5% */
    }
</style>