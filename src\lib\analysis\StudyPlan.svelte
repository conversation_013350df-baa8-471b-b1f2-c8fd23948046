<script lang="ts">
	import { H3, P2, H4, H5 } from '$lib/ui';

	interface Props {
		studyPlan: { comment: string, questionType: string, steps: { title: string, estimatedTime: string, description: string, cards: { link: string, title: string, subtitle: string }[] }[] }[];
		studyPlanTime: number;
		simulation: string;
	}

	let { studyPlan, studyPlanTime, simulation }: Props = $props();

	let realPlan = $derived(studyPlanTime === 7 ? studyPlan.slice(2) : studyPlan);
</script>

<div class="study-plan">
	{#each realPlan as { comment, questionType, steps }, i}
	<div class="week-container">
		<div class="week-intro">
			{#if ((studyPlanTime === 14 || studyPlanTime === 7) && i % 2 == 0) || studyPlanTime === 30}
			{@const isWeekTwo = studyPlanTime === 14 && i == 2}
			<H3>Week {isWeekTwo ? 2 : i + 1}:</H3>
			{/if}
			<H3>{questionType}</H3>
			<P2>{ comment }</P2>
		</div>

		<div class="week-content">
			{#each steps as { title, estimatedTime, description, cards }, index}
			<div class="week-block">
				<div class="block-icon">
					<svg
						width="41"
						height="41"
						viewBox="0 0 41 41"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<circle cx="20.8535" cy="20.0771" r="17.5" stroke="#66E2FF" stroke-width="5" />
					</svg>

					{#if index !== steps.length - 1}
					<svg
						class="straight-line"
						width="5"
						height="100%"
						viewBox="0 0 5 100%"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<line
							x1="2.5"
							y1="1.09278e-07"
							x2="2.49999"
							y2="100%"
							stroke="#66E2FF"
							stroke-width="5"
						/>
					</svg>
					{/if}
				</div>

				<div class="block-content">
					<H4 --text-color="var(--sky-blue)">{title}</H4>
					<P2 --text-color="#888">{estimatedTime}</P2>
					<P2>{description}</P2>
					<div class="block-todo">
						{#each cards as { link, title, subtitle }}
							<a class="card-container" href={ link }>
								<H5>
									<div class="title-wrapper">
										<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
											<path d="M17.8535 9.07715V7.07715C17.8535 5.75107 17.3267 4.4793 16.389 3.54161C15.4514 2.60393 14.1796 2.07715 12.8535 2.07715C11.5274 2.07715 10.2557 2.60393 9.31798 3.54161C8.3803 4.4793 7.85352 5.75107 7.85352 7.07715V9.07715C7.05787 9.07715 6.2948 9.39322 5.7322 9.95583C5.16959 10.5184 4.85352 11.2815 4.85352 12.0771V19.0771C4.85352 19.8728 5.16959 20.6359 5.7322 21.1985C6.2948 21.7611 7.05787 22.0771 7.85352 22.0771H17.8535C18.6492 22.0771 19.4122 21.7611 19.9748 21.1985C20.5374 20.6359 20.8535 19.8728 20.8535 19.0771V12.0771C20.8535 11.2815 20.5374 10.5184 19.9748 9.95583C19.4122 9.39322 18.6492 9.07715 17.8535 9.07715ZM9.85352 7.07715C9.85352 6.2815 10.1696 5.51844 10.7322 4.95583C11.2948 4.39322 12.0579 4.07715 12.8535 4.07715C13.6492 4.07715 14.4122 4.39322 14.9748 4.95583C15.5374 5.51844 15.8535 6.2815 15.8535 7.07715V9.07715H9.85352V7.07715ZM18.8535 19.0771C18.8535 19.3424 18.7482 19.5967 18.5606 19.7843C18.3731 19.9718 18.1187 20.0771 17.8535 20.0771H7.85352C7.5883 20.0771 7.33395 19.9718 7.14641 19.7843C6.95887 19.5967 6.85352 19.3424 6.85352 19.0771V12.0771C6.85352 11.8119 6.95887 11.5576 7.14641 11.37C7.33395 11.1825 7.5883 11.0771 7.85352 11.0771H17.8535C18.1187 11.0771 18.3731 11.1825 18.5606 11.37C18.7482 11.5576 18.8535 11.8119 18.8535 12.0771V19.0771Z" fill="black"/>
										</svg>
										<span>{ title }</span>
									</div>
								</H5>
								<P2>{ subtitle }</P2>
							</a>
						{/each}
				</div>
				</div>
			</div> 
			{/each}
		</div>
	</div>
	{/each}


	<div class="week-container">
		<div class="week-intro">
			<H3>Measure Your Progress</H3>
			<P2>Retake a full-length practice test to see how much your score improved after all your effort.</P2>
		</div>

		<div class="week-content">
			<div class="week-block">
				<div class="block-icon">
					<svg
						width="41"
						height="41"
						viewBox="0 0 41 41"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<circle cx="20.8535" cy="20.0771" r="17.5" stroke="#66E2FF" stroke-width="5" />
					</svg>
				</div>
		
				<div class="block-content">
					<H4 --text-color="var(--sky-blue)">Next Simulation</H4>
					<P2>Full-length tests are a good way to build up your mental stamina and your ability to concentrate for the test day.</P2>
					<P2>The analysis of the next simulation will also give you insights into what you should focus on in the next period.</P2>
					<div class="block-todo">
						<a class="card-container" href={ null }>
							<H5>Simulation {parseInt(simulation) + 2}</H5>
							<P2>Replicate the exact format and question styles from Bluebook's practice tests.</P2>
						</a>
					</div>
				</div>
			</div> 
		</div>
	</div>

</div>

<style>
	.study-plan {
		display: flex;
		flex-direction: column;
		gap: 2.5rem;
	}

	.week-container {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.week-intro {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.week-content {
		display: flex;
		flex-direction: column;
		gap: 3.375rem;
	}

	.week-block {
		display: inline-flex;
		gap: 0.625rem;
	}

	.block-icon {
		display: flex;
		flex-direction: column;
		justify-content: start;
		align-items: center;
		translate: 0 -0.25rem;

		position: relative;
	}

	.straight-line {
		position: absolute;
		top: 38px;
		height: calc(100% + 1.3rem)
	}

	.block-content {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.block-todo {
		display: inline-flex;
		gap: 0.625rem;
		flex-wrap: wrap;
	}

	.card-container {
        display: flex;
        flex-direction: column;
        gap: 0.375rem;
        background-color: #FFF;
        padding: 0.625rem;

        border: 1px solid var(--charcoal);
        border-radius: 0.375rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		background-color: #F5F5F5;
	}

	.title-wrapper {
		display: inline-flex;
		gap: 0.625rem;
		align-items: center;
		height: 100%;
	}
</style>
