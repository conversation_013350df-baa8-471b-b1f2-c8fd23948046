<script lang="ts">
    interface Props {
        circles?: import('svelte').Snippet;
        header?: import('svelte').Snippet;
    }

    let { circles, header }: Props = $props();
</script>

<div class="container">
    <div class="section">
        {@render circles?.()}
    </div>

    {@render header?.()}
</div>

<style>
    .container {
        display: flex;
        flex-direction: column;
        padding: 2.5rem;
        flex: 1 0 auto;
        justify-content: center;
        text-align: center;
        gap: 2rem;
        
        background-color: var(--bg-color);
        border: 1px solid var(--pitch-black);
        border-radius: 0.5rem;
        box-shadow: 0.25rem 0.25rem var(--pitch-black);
    }

    .section {
        display: inline-flex;
        gap: 2rem;
        justify-content: space-around;
        align-items: center;
    }
</style>