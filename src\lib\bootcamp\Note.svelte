<script>
	// import <PERSON><PERSON>yper from "$lib/study/NoteHyper.svelte";
    import AnswerBox from "$lib/mockTest/ui/AnswerBox.svelte";
	import P2 from "$lib/ui/typography/P2.svelte";

    /** @type {{title: any, notes: any, answers?: any, hyperlink: any, slug: any}} */
    let {
        title,
        notes,
        answers = null,
        hyperlink,
        slug
    } = $props();

    let letters = ['A', 'B', 'C', 'D'];
    let selections = $state(new Array(answers?.length).fill(null));
    let checked = $state(new Array(answers?.length).fill([]));
    let prompts = $state([]);

    
    // TODO
    function check(id) {
        if (selections[id] === null || checked[id].includes(selections[id])) {
            prompts[id] = 'empty';
            return;
        }

        // Add the choice to the checked array
        checked[id] = [...checked[id], selections[id]];

        if (selections[id] === answers[id] || typeof answers[id] === "object" && (answers[id].recommended.concat(answers[id].possible)).includes(selections[id]))  {
            prompts[id] = "correct";
        }  else prompts[id] = "incorrect";

        // Reset selection
        selections[id] = null;
    }

    function select(id, index) {
        if (selections[id] === null || selections[id] !== index) {
            selections[id] = index;
        } else if (selections[id] === index) selections[id] = null;
        
        prompts[id] = '';  
    }
</script>

<!-- * Katex for math -->
<svelte:head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css" integrity="sha384-wcIxkf4k558AjM3Yz3BBFQUbk/zgIYC2R0QpeeYb+TwlBVMrlgLqwRjRtGZiK7ww" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js" integrity="sha384-hIoBPJpTUs74ddyc4bFZSM1TVlQDA60VBbJS0oA934VSz82sBx1X7kSx2ATBDIyd" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous">
    </script>
</svelte:head>

<svelte:window onload={renderMathInElement(document.body, {
    delimiters: [
        {left: "$$", right: "$$", display: true},
        {left: "$", right: "$", display: false},
    ],
        
    throwOnError: false
})} />

<div class="note">
    <div class="title">{title}</div>

    <!-- <NoteHyper {hyperlink} /> -->
    {#each notes as section}
        <div class="section">
        <!-- Heading 1 -->
        {#if section.title.type === "Heading 1"}
            <h1>{@html section.title.content}</h1>
            <hr>
        {/if}
    
        <!-- Heading 2 -->
        {#if section.title.type === "Heading 2"}
            <h2>{@html section.title.content}</h2>
            <hr>
        {/if}

        <div class="section-note">
            {#each section.content as note, index}
                <!-- Paragraph -->
                {#if note.type === 'Paragraph'}
                    <div class="paragraph">{@html note.content}</div>
                {/if}
                
                <!-- List -->
                {#if note.type === 'List' || note.type === 'List Lv2' || note.type === 'List Lv3'}
                    <ul class={`${note.type === 'List Lv3' ? 'level-3' : note.type === 'List Lv2' ? 'level-2' : ''} ${['Paragraph', 'List Lv2', 'List', 'List Lv3'].includes(section.content[index !== 0 ? index - 1 : index].type) ? 'negative-margin' : '' }`}>
                        {#each note.content as item}
                        <li>{@html item}</li>
                        {/each}
                    </ul>
                {/if} 

                <!-- Card -->
                {#if note.type === 'Card'}
                    <div class="card-wrapper">
                        <div class="card">
                            {#each note.content as card}
                                {#if card.type === 'Card Title'}
                                    <div class="card-title">{@html card.content}</div>
                                {/if}
                                {#if card.type === 'Card Paragraph'}
                                    <div class="card-paragraph">
                                    {#each card.content as cardParagraph}
                                        <span class={cardParagraph.type}>{@html cardParagraph.content}</span>
                                    {/each}
                                    </div>
                                {/if}
                            {/each}
                        </div>
                    </div>
                {/if}

                <!-- Table -->
                {#if note.type === "Table"}
                <div class="table-wrapper">
                    <table class="table">
                        <tbody>
                            {#each note.content as row }
                                <tr>
                                    {#each row as item }
                                        <td>{@html item}</td>
                                    {/each}
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                </div>
                {/if}

                {#if note.type === "Ordered List"}
                <ol>
                    {#each note.content as item }
                        <li>{@html item}</li>
                    {/each}
                </ol>
                {/if}

                <!-- Image -->
                {#if note.type === "Image"}
                    <div class="img-container">
                        <img src="/note-graphs/{note.content.pc}" alt={note.content.pc} class="pc-image">
                        <img src="/note-graphs/{note.content.mobile}" alt={note.content.mobile} class="mobile-image">
                    </div>
                {/if}

                <!-- Image -->
                {#if note.type === "Figure"}
                    <div class="figure-container">
                        <img src="/note-graphs/{note.content}" alt={note.content} class="figure-image">
                    </div>
                {/if}

                <!-- Question -->
                {#if note.type === "Question" || note.type === "SNQuestion"}
                    <div class="question-wrapper">
                        <div class="question-container">    
                            <div class="question-title">{note.content.title}</div>
                            
                            <div class="question">

                                <!-- R&W Questions -->
                                {#if slug < 8}
                                    <div class="passage-wrapper">
                                        {#if note.type === "Question"}

                                            {#if note.content.graph}
                                                <img src={`/note-graphs/${note.content.graph}`} alt="graph" class="graph">
                                            {/if}

                                            {#if note.content.intro}
                                                <div class="intro">{@html note.content.intro}</div>
                                            {/if}

                                            {#if note.content.passage2}
                                                <div class="paired-text">Text 1</div>   
                                            {/if}

                                            <div class={`question-passage ${note.content.passage2 ? 'smaller' : ''}`}>
                                                {@html note.content.passage}
                                            </div>

                                            {#if note.content.passage2}
                                                <div class="paired-text text-2">Text 2</div>
                                            {/if}

                                            {#if note.content.passage2}
                                                <div class={`question-passage ${note.content.passage2 ? 'smaller' : ''}`}>
                                                    {@html note.content.passage2}
                                                </div>
                                            {/if}

                                        <!-- Student's Notes -->
                                        {:else}
                                            <div class="qn-passage">
                                                <div>While researching a topic, a student has taken the following notes.</div>
                                                <ul class="qn-list">
                                                    {#each note.content.passage as point}
                                                        <li>{@html point}</li>
                                                    {/each}
                                                </ul>
                                            </div>
                                        {/if}
                                    </div>
                                    <div class="vr"></div>
                                {/if}

                                <div class="question-right">

                                    <!-- If math -->
                                    {#if slug >= 8 && slug <= 10}
                                        {#if note.content.intro}
                                            <div class="intro">{@html note.content.intro}</div>
                                        {/if}

                                        {#if Array.isArray(note.content.graph)}
                                            <div class={!note.content.choices ?  "spr-table-wrapper" : "table-wrapper"}>
                                                <table class={!note.content.choices ?  "spr-table" : "table"}>
                                                    <tbody>
                                                        {#each note.content.graph as row }
                                                            <tr>
                                                                {#each row as item }
                                                                    <!-- TODO: Implement auto span -->
                                                                    <td colspan={ item == "<b>Volume ($cm^3$)</b>" ? 6 : 1 }>{@html item}</td>
                                                                {/each}
                                                            </tr>
                                                        {/each}
                                                    </tbody>
                                                </table>
                                            </div>
                                        {:else if note.content.graph}
                                            <div class="graph-math-container">
                                                <img src={`/note-graphs/${note.content.graph}`} alt="graph" class="graph-math">
                                            </div>
                                        {/if}
                                    {/if}

                                    <div class="question-question">
                                        <P2>{@html note.content.question}</P2>
                                    </div>

                                    <!-- MCQ -->
                                    {#if note.content.choices}
                                        {#each note.content.choices as choice, index}
                                            {#if answers[note.content.id] !== index}
                                                <button 
                                                    class="question-choice question-choice-incorrect" class:question-choice-selected={selections[note.content.id] === index} 
                                                    onclick={() => select(note.content.id, index)} 
                                                    disabled={checked[note.content.id].includes(index) || checked[note.content.id].includes(answers[note.content.id])}
                                                >
                                                    <div class="question-choice-letter">{letters[index]}</div>
                                                    <div class="question-choice-text">{@html choice}</div>
                                                </button>
                                            {:else}
                                                <button 
                                                class="question-choice question-choice-correct" class:question-choice-selected={selections[note.content.id] === index}
                                                onclick={() => select(note.content.id, index)}
                                                disabled={checked[note.content.id].includes(index) || checked[note.content.id].includes(answers[note.content.id])}
                                                >
                                                    <div class="question-choice-letter">{letters[index]}</div>
                                                    <div class="question-choice-text">{@html choice}</div>
                                                </button>
                                            {/if}
                                        {/each}
                                    <!-- SPR -->
                                    {:else}
                                        <AnswerBox i={note.content.id} studentAnswers={selections} isNote={true} />
                                    {/if}
                                    
                                </div>
                            </div>                            

                            {#if prompts[note.content.id] === 'empty'}
                                <div class="prompt prompt-empty">It looks like you left your answer blank.</div>
                            {:else if prompts[note.content.id] === 'incorrect'}
                                <div class="prompt prompt-incorrect">Incorrect answer. Wanna try again?</div>
                            {:else if prompts[note.content.id] === 'correct'}
                                <div class="prompt prompt-correct">Correct answer. Continue reading to see explanation in the notes.</div>
                            {:else}
                                <div class="prompt-placeholder"></div>
                            {/if}

                            <button 
                                class="check"
                                class:check-blue={selections[note.content.id] !== null}
                                class:check-green={checked[note.content.id]?.includes(answers[note.content.id]) || checked[note.content.id]?.some( value => answers[note.content.id].recommended?.concat(answers[note.content.id].possible).includes(value) )}
                                onclick={() => check(note.content.id)} 
                                disabled={checked[note.content.id]?.includes(answers[note.content.id]) || checked[note.content.id]?.some( value => answers[note.content.id].recommended?.concat(answers[note.content.id].possible).includes(value) )}>
                                Check
                            </button>

                        </div>
                    </div>
                {/if}
            {/each}    
            </div>
        </div>
    {/each}
    </div>

<style>
    .img-container {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .pc-image {
        width: 80%;
        max-width: 1024px;
    }

    .mobile-image {
        display: none;
    }

    .figure-container {
        aspect-ratio: 16 / 9;
        max-height: 320px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8px 0;
    }

    .figure-image {
        object-fit: contain;
        width: 100%;
        height: 100%;
    }

    /* width */
    ::-webkit-scrollbar {
        height: 8px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }

    .note {
        position: relative;
        width: 100%;
        max-width: 1200px;
        height: fit-content;
        margin-left: 320px;
        padding: 64px 20px 91px 20px;
        display: flex;
        flex-direction: column;
    }

    .title {
        color: #000;
        font-family: "Inter";
        font-size: 36px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .section {
        margin-top: 55px;
    }

    h1 {
        color: #000;
        /* Desktop/H3 Desktop */
        font-family: "Inter";
        font-size: 28px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    h2 {
        color: #000;
        /* Desktop/H3 Desktop */
        font-family: "Inter";
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    hr {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin: 8px 0 16px 0;
    }

    .paragraph {
        color: #000;
        font-family: "Open Sans";
        font-size: 18px;
        font-style: normal;
        font-weight: 450;
        line-height: 27px;
        margin-bottom: 20px;
    }

    ul {
        list-style-type: circle;
        margin-left: 1.8em;
        padding-left: 0;
        margin-bottom: 20px;
    }

    .negative-margin {
        margin-top: -20px;
    }

    .level-2 {
        margin-left: 3.6em;
    }

    .level-3 {
        margin-left: 5.4rem;
    }

    li {
        color: #000;
        font-family: "Open Sans";
        font-size: 18px;
        font-style: normal;
        font-weight: 450;
        line-height: 27px;
    }

    ol {
        margin-left: 48px;
    }

    .card-wrapper {
        width: 100%;
        display: flex;
        padding: 16px;
        align-items: center;
        gap: 10px;
        border-radius: 8px;
        border: 1px solid #D9D9D9;
        margin-bottom: 20px;
        overflow: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .card {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        min-width: fit-content;
    }

    .card-title {
        color: #000;
        font-family: "Inter";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .card-paragraph {
        color: #000;
        font-family: "Open Sans";
        font-size: 18px;
        font-style: italic;
        font-weight: 450;
        line-height: 27px;
    }

    .card-correct {
        font-weight: bold;
        background-color: #B6D7A8;
    }

    .card-incorrect {
        font-weight: bold;
        color: #FF0000;
    }

    .card-highlight {
        background-color: #66E2FF;
    }

    .table-wrapper {
        width: 100%;
        overflow-x: auto;
        margin-bottom: 16px;
        display: flex;
        justify-content: center;
    }

    .table {
        width: 80%;
        table-layout: auto;
    }

    td {
        text-overflow: clip;
        font-family: "Open Sans";
        font-size: 18px;
        text-align: center;
        padding: 8px;
    }

    table, td {
        border: 1px solid #000;
        border-collapse: collapse;
    }

    .question-wrapper {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-bottom: 24px;
        padding: 2rem;
        border-radius: 0.75rem;
        border: 1px solid var(--pitch-black, #000);
        background-color: #FFF;
        box-shadow: 0.25rem 0.25rem 0px var(--pitch-black, #000);
    }

    .question-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .question-title {
        width: fit-content;
        background: var(--web-color-light-sky-blue, #DAF8FF);
        color: var(--charcoal, #333);
        /* Desktop/p3 Desktop */
        font-family: "Open Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 450;
        line-height: 24px; /* 150% */
        padding: 0 0.625rem;
        border-radius: 0.75rem;
        border: 1px solid var(--pitch-black, #000);
    }

    .graph {
        aspect-ratio: initial;
        width: 100%;
        margin-bottom: 12px;
    }

    .graph-math-container {
        aspect-ratio: 16 / 9;
        max-height: 300px;
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 12px 0;
    }

    .graph-math {
        object-fit: contain;
        width: 90%;
        height: 100%;
    }

    .question {
        width: 100%;
        max-width: 900px;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .passage-wrapper {
        width: 100%;
        display: flex;
        flex-direction: column;
        width: 100%;
        color: #000;
        /* Desktop/p2 Desktop */
        font-family: "Open Sans";
        font-size: 18px;
        font-style: normal;
        font-weight: 450;
        line-height: 27px; /* 150% */
    }

    .intro {
        font-family: "Open Sans";
        font-size: 18px;
        font-weight: 450;
        line-height: 27px; /* 150% */
        margin-bottom: 12px;
        font-size: 17px;
    }

    .paired-text {
        font-size: 18px;
        font-weight: 700;
    }

    .text-2 {
        margin-top: 20px;
    }

    .question-passage {
        width: 100%;
        color: #000;
        /* Desktop/p2 Desktop */
        font-family: "Open Sans";
        font-size: 18px;
        font-style: normal;
        font-weight: 450;
        line-height: 27px; /* 150% */
    }

    .smaller {
        font-size: 18px;
    }

    .qn-passage {
        display: flex;
        flex-direction: column;
        color: #000;
        /* Desktop/p2 Desktop */
        font-family: "Open Sans";
        font-size: 18px;
        font-style: normal;
        font-weight: 450;
        line-height: 27px; /* 150% */   
        gap: 20px;
    }

    .vr {
        align-self: stretch;
        width: 1px;
        background: #000;
    }

    .question-right {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        width: 100%;
    }

    .question-question {
        color: #000;

    }

    .question-choice {
        display: flex;
        align-items: center;
        border: 1px solid #000;
        border-radius: 0.5rem;
        box-shadow: 0.25rem 0.25rem 0px var(--pitch-black, #000);
        background: #FFF;
        width: 100%;
    }

    .question-choice:active:enabled {
        box-shadow: none;
        transform: translate(0.25rem, 0.25rem);
    }


    .question-choice-correct:disabled {
        background: var(--web-color-light-aquamarine, #D1FFEE);
        cursor: not-allowed;
    }

    .question-choice-incorrect:disabled {
        background: var(--rose, #FFDAF1);
        cursor: not-allowed;
    }

    .question-choice-selected {
        background: var(--sky-blue, #66E2FF);
    }

    .question-choice-letter {
        display: flex;
        width: 50px;
        padding: 10px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        color: #000;

        /* Desktop/p2 Desktop */
        font-family: "Open Sans";
        font-size: 18px;
        font-style: normal;
        font-weight: 450;
        line-height: 27px; /* 150% */
    }

    .question-choice-text {
        text-align: start;
        width: 100%;
        padding: 10px;
        border-left: 1px solid #000;
        color: #000;
        font-family: "Open Sans";
        font-size: 18px;
        font-weight: 450;
        line-height: 27px; /* 150% */
    }

    .check {
        width: 102.5px;
        height: 50.5px;
        display: flex;
        padding: 12px 24px;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        border: 1px solid #000;
        background-color: #FFF;
        box-shadow: 4px 4px 0px 0px #000;
        color: #000;
        text-align: center;

        /* Desktop/Button1 Desktop */
        font-family: "Open Sans";
        font-size: 18px;
        font-weight: 600;
    }

    .check:active:enabled {
        box-shadow: 0 0 0 0;
        transform: translate(4px, 4px);
    }

    .check:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }
    
    .check-blue {
        background-color: var(--web-color-sky-blue, #66E2FF) !important;
        transition: 0.05s;    
    }

    .check-green {
        background-color: #D1FFEE;
        transition: 0.05s;
    }

    .prompt {
        color: #000;

        /* Desktop/p3 Desktop */
        font-family: "Open Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 450;
        line-height: 24px; /* 150% */
    }

    .prompt-placeholder {
        height: 24px;
    }

    @media (max-width: 1023px) {
        .note {
            margin-left: 0;
            padding-top: 100px;
        }
    }

    @media (max-width: 768px) {
        .question {
            flex-direction: column;
            gap: 10px;
        }

        .vr {
            display: none;
        }

        .question-right {
            gap: 10px;
        }
    }

    @media (max-width: 540px) {
        .pc-image {
            display: none;
        }

        .mobile-image {
            width: 80%;
            max-width: 1024px;
            display: block;
        }
    }
</style>