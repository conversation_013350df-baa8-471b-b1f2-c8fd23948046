<script>
	import { H2, H4, P1, <PERSON><PERSON><PERSON><PERSON><PERSON>, P2, P3 } from "$lib/ui";
	import { MinitestCard, SprintCard, CommitmentCard } from "$lib/ui/pricing-cards";
	import WaitlistForm from "$lib/ui/WaitlistForm.svelte";
    import SectionWrapper from "../SectionWrapper.svelte";
	import TestimonialCard from "./TestimonialCard.svelte";

    import phucAvatar from "$lib/assets/testimonials/phuc-avatar.webp?enhanced";
    import phucTestimonial from "$lib/assets/testimonials/phuc-testimonial.webp?enhanced&w=720;420";
    import sonAvatar from "$lib/assets/testimonials/son-avatar.webp?enhanced";
    import sonTestimonial from "$lib/assets/testimonials/son.webp?enhanced&w=720;420";
    import tpAvatar from "$lib/assets/testimonials/tp-avatar.webp?enhanced";
    import tp from "$lib/assets/testimonials/tp.webp?enhanced&w=720;420";
    import dhhAvatar from "$lib/assets/testimonials/dhh-avatar.webp?enhanced";
    import dhh from "$lib/assets/testimonials/dhh.webp?enhanced&w=720;420";
    import niaAvatar from "$lib/assets/testimonials/nia-avatar.webp?enhanced";
    import kn2Avatar from "$lib/assets/testimonials/kn2-avatar.webp?enhanced";
</script>

<SectionWrapper --bg-color=var(--pitch-black) --padding-top=8.6875rem --padding-bottom=18.875rem>
    <div class="testimonial-text">
        <H2>The DSAT<span class="gradient">16</span> Simulation</H2>
        <P1>Built based on my own learning and teaching experience</P1>
    </div>

    <div class="testimonials">
        <TestimonialCard 
            name={"Phuc Le"}
            profilePicUrl={phucAvatar}
            oldScore=1310 
            newScore=1600 
            hasDescription={true}
            review="Most people don’t have the self-awareness to figure themselves out, and most people don’t have the patience to count how many mistakes they got in each question type.
That’s when I thought: let’s delegate this boring task to technology so students can spend their time actually studying. 
After 2 years, that single thought turned into DSAT16."
            imageUrl={phucTestimonial}
        >
            {#snippet description()}
                <div>
                    <P3>DSAT16 founder</P3>
                    <P3>100% scholarship Aalto University, Finland</P3>
                </div>
            {/snippet}
        </TestimonialCard>

        <TestimonialCard 
            name="d****o****"
            profilePicUrl={sonAvatar}
            oldScore=1410 
            newScore=1550 
            review="In November, I was completely blind to Logical Reasoning :))), it was really haunting, I did it like playing the lottery but after following Phuc Le’s tips, I really improved, and how much I improved, you can see in my score."
            imageUrl={sonTestimonial}
        />

        <TestimonialCard 
            name="p****3***"
            profilePicUrl={tpAvatar}
            oldScore=1230 
            newScore=1400
            imageUrl={tp}
        />

        <TestimonialCard 
            name="d***h***h**"
            profilePicUrl={dhhAvatar}
            oldScore=1300 
            newScore=1400
            imageUrl={dhh}
        />

        <TestimonialCard 
            name="n***i***a**"
            profilePicUrl={niaAvatar}
            oldScore=900 
            newScore=1270 
        />

        <TestimonialCard 
            name="k***n***2*"
            profilePicUrl={kn2Avatar}
            oldScore=1100 
            newScore=1500 
        />
    </div>

    <div class="dsat16-impact">
        <H4>Before DSAT16 Simulation:</H4>
        <P1Wrapper>
            <ul>
                <li>Feel lost, don’t know what to do.</li>
                <li>Waste hours without any clear direction.</li>
                <li>Always anxious.</li>
                <li>“I make mistakes because my English is terrible.”</li>
                <li>“If my score increases, I just got lucky. If it decreases, it’s proof that I’m bad at SAT.”</li>
            </ul>
        </P1Wrapper>
        <H4>With DSAT16 Simulation:</H4>
        <P1Wrapper>
            <ul>
                <li>I understand my strengths and weaknesses.</li>
                <li>I have a clear and well-structured study plan.</li>
                <li>I am confident that I can conquer the SAT, one question type at a time.</li>
                <li>“My mistakes tell me where I need to work on.”</li>
                <li>“My score increase gives me a boost of dopamine to keep me going.”</li>
                <li>“If it decreases, it gives me feedback that I need to find a more effective way to solve the problem.”</li>
            </ul>
        </P1Wrapper>
    </div>


    <div class="pricing" id="pricing">
        <SprintCard />
        <CommitmentCard />
    </div>

    <div class="waitlist" id="waitlist">
        <div class="waitlist-box">
            <P2 isBold={true}>WAITLIST</P2>
        </div>
            <H2>Want to be notified?</H2>
            <div class="waitlist-subtitle">
                <P1>Enter your email and you’ll be the first to know when DSAT16 releases new features.</P1>
            </div>
            <WaitlistForm --form-max-width="26.875rem" --box-shadow-color="white"/>
    </div>
</SectionWrapper>

<style>
    .testimonial-text {
        display: flex;
        flex-direction: column;
        gap: 0.4375rem;
        color: white;
        text-align: center;
        margin-bottom: 12rem;
    }

    
    .gradient {
        background: linear-gradient(90deg, var(--sky-blue) 0%, var(--rose) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .testimonials {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4rem;
        margin-bottom: 12.75rem;
    }

    .dsat16-impact {
        border: 0.1875rem solid var(--pitch-black);
        border-radius: 1rem;
        background-color: var(--light-yellow);
        box-shadow: 1.25rem 1.25rem 0 var(--sky-blue);
        padding: 2rem;
        width: 100%;
        max-width: 40rem;
        margin-bottom: 18rem;

        display: flex;
        flex-direction: column;
        align-self: center;
        gap: 2rem;
    }

    ul {
        margin-left: 1.5rem;
    }

    .pricing {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 1.5rem;
    }

    .waitlist {
        display: flex;
        flex-direction: column;
        text-align: center;
        align-items: center;
        justify-content: center;
        color: white;
        max-width: 35.125rem;
        padding-top: 10rem;
    }

    .waitlist-box {
        background-color: white;
        box-shadow: 0.25rem 0.25rem 0 var(--sky-blue);
        color: var(--rose);
        border: 1px solid var(--pitch-black);
        border-radius: 0.25rem;
        padding: 0.5rem 1rem;
        width: fit-content;
        margin-bottom: 1.625rem;
    }

    .waitlist-subtitle {
        margin: 1rem 0 2.3125rem 0;
        text-wrap: pretty;
    }

    @media (max-width: 1024px) {
        .pricing {
            flex-direction: column;
            gap: 2rem;
        }
    }

    @media (max-width: 768px) {
        .dsat16-impact {
            box-shadow: 0.25rem 0.25rem 0 var(--sky-blue);
            margin-left: -0.25rem;
        }
    }
</style>