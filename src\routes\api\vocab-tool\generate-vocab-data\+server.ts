import { json } from "@sveltejs/kit";
import { Type, type GenerateContentConfig } from "@google/genai";
import { supabase } from "$lib/server/supabase";
import type { Database } from "$lib/types/supabase.types.js";
import { geminiAI } from "$lib/server";
import { redis } from "$lib/server";
import { Ratelimit } from "@upstash/ratelimit";

export const POST = async ({ request }) => {
    const { word, uid } = await request.json();

    const rateLimit = new Ratelimit({
        redis: redis,
        limiter: Ratelimit.slidingWindow(60, "1 h"),
    })

    const { success } = await rateLimit.limit(uid);
    if (!success) {
        return json({ message: "Slow down! You've made too many requests. Please try again in an hour.", success: false });
    }

    const typo = await checkForTypo(word)
      .catch((e) => { console.error(e) });

    if (typo == "-1") {
        return json({ message: "That is not a word.", success: false });
    } else if (typo !== word) {
        return json({ message: "Seems like you made a typo. Did you mean <b>" + typo + "</b>?", success: false });
    }

    // Check if word already exists in database
    const { data: existingVocab, error: checkError } = await supabase
        .from('vocab')
        .select('vocabId')
        .eq('vocab', word.toLowerCase())
        .limit(1)
        .maybeSingle();

    if (checkError) {
        console.error('Error checking existing vocab:', checkError);
        return json({ message: "An error occurred while checking the database. Please contact support.", success: false });
    }

    if (existingVocab?.vocabId) {
        console.log(`The word ${word} already exists in database. Returned existing vocabId: ${existingVocab.vocabId}.`);
        return json({ success: true, vocabId: existingVocab.vocabId });
    }

    const data = await generateVocabData(word);

    // Parse the generated vocab data and insert into Supabase
    try {
        const vocabData: Database["public"]["Tables"]["vocab"]["Insert"] = JSON.parse(data);
        vocabData.vocab = word.toLowerCase();
        
        const { data: insertedData, error } = await supabase
            .from('vocab')
            .insert(vocabData)
            .select('vocabId')
            .single();

        if (error) {
            console.error('Error inserting vocab data:', error);
            return json({ message: "An error occurred while saving the data. Please contact support.", success: false });
        }

        return json({ message: "Card added successfully.", success: true, vocabId: insertedData.vocabId });
    } catch (parseError) {
        console.error('Error parsing vocab data:', parseError);
        return json({ message: "An error occurred while saving the data. Please contact support.", success: false });
    }
}


async function checkForTypo(word: string) {
    const response = await geminiAI.models.generateContent({
        model: "gemini-2.0-flash-lite",
        contents: word,
        config: {
            temperature: 0,
            maxOutputTokens: 4,
            systemInstruction: [
                {
                    text: `You are a typo checker. You are given a word and you need to check if it is a typo. If it is, return the correct word in lowercase. If it is not a typo, return the word in lowercase. If it is not a word, return -1. Return nothing else.`
                }
            ]
        }  
    })
    
    return response.text.trim();
}

const config: GenerateContentConfig = {
    temperature: 0.8,
    thinkingConfig: {
        thinkingBudget: 0
    },
    responseMimeType: 'application/json',
    responseSchema: {
      type: Type.OBJECT,
      required: ["meaning", "charge", "difficulty", "hint1", "hint2"],
      properties: {
        meaning: {
          type: Type.STRING,
        },
        charge: {
          type: Type.STRING,
        },
        difficulty: {
          type: Type.NUMBER,
        },
        hint1: {
          type: Type.STRING,
        },
        hint2: {
          type: Type.STRING,
        },
      },
    },
    systemInstruction: [
        {
          text: `You are an expert vocabulary tutor, skilled at providing clear definitions and contextual examples to help students prepare for the SAT.

For each word submitted by the user, return the following fields:
1. meaning: a concise definition suitable for SAT understanding
2. charge: the connotation of the word. It is one of Neutral, Positive, or Negative. Do not use any other connotation.
3. difficulty: an integer from 1 to 4
4. hint1: a 60-word passage that contains the vocabulary word. The passage follow the structure described below. The vocabulary word should be wrapped in \`<b>...</b>\` tags.
5. hint2: a passage of about 80 words with context using the vocabulary word. The vocabulary word should be wrapped in \`<b>...</b>\` tags, and helpful context clues (other words) should be wrapped in \`<mark style="background-color: #fff9c4">...</mark>\` to assist the reader in inferring the meaning.

Passage Structure:
- Except for human names, all other names (e.g. scientific species names, novel names, poem names, exhibition names, song names, architecture names) are italic. This should be wrapped in '<i>...</i>' at the start and end of the name. Concepts that have normal English names are not italic.
- The passage topic is one of Social Science, Natural Science, or Humanities. 
- Examples include Scientific Research & Studies, Social Science, Economic Analysis, Environmental Science, Human Behavior & Psychology, Literature, and Art Interpretation.
- The passage refers to actual real studies, scientific articles, or books. Do not make these up. Do not state where the study was published.
- The passage is less analytical and argumentative. It is more descriptive and straightforward.
- The passage is sprinkled with context clues (transition words, punctuations, synonyms, paraphrased versions, contrasting words, etc) to help the student figure out the meaning of the word.
- Try not to introduce the word in the 1st sentence, though that is also entirely ok.
- Don't use smart words like "meticulous" or "intricate" just because you can.

The output should be a JSON object containing the above fields. You only have 128 tokens of thinking.`,
        }
    ],
  };
const model = 'gemini-2.5-flash';

async function generateVocabData(word: string) {

    const response = await geminiAI.models.generateContent({
        model,
        config,
        contents: word
    });
    let data = response.text;
    return data;    
}