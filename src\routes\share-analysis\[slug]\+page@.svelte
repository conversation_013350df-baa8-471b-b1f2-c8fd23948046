<script>
	import Overview from "$lib/analysis/Overview.svelte";
    import { Top } from "$lib/mockTest/walkthrough";
	import { H2, P1, Button } from "$lib/ui";
    
	/** @type {{data: any}} */
	let { data } = $props();
    let skills = $derived([
		{
			sectionTitle: 'Reading',
			isVerbal: true,
			sections: data.scoreOfTypes.slice(0, 4)
		},
		{
			sectionTitle: 'Writing',
			isVerbal: true,
			sections: data.scoreOfTypes.slice(4, 8)
		},
		{
			sectionTitle: 'Math',
			isVerbal: false,
			sections: data.scoreOfTypes.slice(8)
		}
	]);

    // Analysis Mode
    let isInAnalysis = false;
</script>

<svelte:head>
    <title>{data.name}'s Analysis</title> 
</svelte:head>

<Top title="{data.title}" {isInAnalysis} hasAnalysis={false} isShareAnalysis={true} />

<div class="wrapper">
	<div class="container">
		<div class="name-wrapper">
			<H2>{data.name}'s Result</H2>
		</div>
		<Overview student={data.student} {skills} isSharing={true} />
		<div class="cta">
			<img 
				loading="lazy"
				src="/star.png"
				alt=""
				class="star"
			/>
				
			<div class="cta-text">
				<H2>What about you?</H2>
				<P1>Don’t let full-length practice tests go to waste. Analyze them to fully understand your strengths and weaknesses, and what to do to maximize your score.</P1>
				<a href="/sign-up"><Button>Start Now</Button></a>
			</div>
		</div>
	</div>


</div>
<style>
	.wrapper {
		display: flex;
		width: 100%;
		padding: 4rem 2rem;
		justify-content: center;
		align-items: center;
		background-color: #F5FDFF;
	}

	.container {
		padding: 1.875rem;
		border: 1px solid var(--pitch-black);
		border-radius: 1rem;
		display: flex;
		flex-direction: column;
		gap: 2.5rem;
		background-color: white;
		max-width: 54rem;
	}

	.cta {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 2rem;

		padding: 1.875rem;
		border: 1px solid var(--pitch-black);
		border-radius: 0.75rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
	}

	.star {
		max-width: 9rem;
	}

	.cta-text {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		gap: 1rem;
	}

	@media (max-width: 540px) {
		.wrapper {
			padding: 2rem 1rem;
			overflow: hidden;
		}

		.container {
			padding: 0;
			border: none;
			align-items: center;
		}

		.name-wrapper {
			text-align: center;
			text-wrap: balance;
		}

		.cta {
			padding: 1rem;
		}
	}
</style>