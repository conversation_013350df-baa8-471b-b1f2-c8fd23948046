import posthog from 'posthog-js';
import type { HandleClientError } from '@sveltejs/kit';
import { initializeFirestore, persistentLocalCache, persistentMultipleTabManager } from 'firebase/firestore';
import { app } from './lib/firebase/config';
import { dev } from '$app/environment';

initializeFirestore(app, {
    localCache: persistentLocalCache({
        tabManager: persistentMultipleTabManager(),
    }),
});


export const handleError: HandleClientError = ({ error, status }) => {
    // SvelteKit 2.0 offers a reliable way to check for a 404 error:
    if (status !== 404) {
        posthog.captureException(error);
    }

    if (dev) console.error(error);
};