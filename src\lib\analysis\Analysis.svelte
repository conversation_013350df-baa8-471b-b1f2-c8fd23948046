<script>
    // import domtoimage from 'dom-to-image';
    import Srand from "random-seed";
	import SkillSummary from "./SkillSummary.svelte";
	import { H3, H5, P2, P2<PERSON><PERSON><PERSON>, P3 } from "$lib/ui";
	import Button from "$lib/ui/Button.svelte";
	import { SprintCard } from "$lib/ui/pricing-cards";
	import CommitmentCard from "$lib/ui/pricing-cards/CommitmentCard.svelte";

    /** @type {{data: any}} */
    let { data } = $props();
    let skills = $derived([
		{
			sectionTitle: 'Reading',
			isVerbal: true,
			sections: data.scoreOfTypes.slice(0, 4)
		},
		{
			sectionTitle: 'Writing',
			isVerbal: true,
			sections: data.scoreOfTypes.slice(4, 8)
		},
		{
			sectionTitle: 'Math',
			isVerbal: false,
			sections: data.scoreOfTypes.slice(8)
		}
	]);
    // let analysis;
    let isActivationOpen = false;
    // let isAnalysisImageOpen = false;
    const scores = [400, 400, 430, 530, 630, 710, 780, 850, 910, 960, 1010, 1080, 1140, 1200, 1280, 1340, 1400, 1450, 1480, 1520, 1560];
    const verbalScores = [200, 200, 220, 270, 320, 360, 390, 420, 450, 480, 510, 540, 570, 600, 630, 660, 690, 720, 740, 760, 780];

    const rand = Srand.create(data.student.user);
    let predictedTotalScore = scores[data.student.score] + ( rand.intBetween(-2, 2) * 10 );
    let predictedVerbalScore = verbalScores[data.scoreOfTypes.slice(0, 8).reduce( (sum, cur) => sum + cur.score, 0 )];
    let predictedMathScore = $derived(predictedTotalScore - predictedVerbalScore);

    let worstTopic = data.scoreOfTopics.sort( (a, b) => (a.score / a.count) - (b.score / b.count))[0];
    let worstReading = data.scoreOfTypes.slice(0, 4).sort( (a, b) => (a.score / a.count) - (b.score / b.count))[0];
    let worstWriting = data.scoreOfTypes.slice(4, 7).sort( (a, b) => (a.score / a.count) - (b.score / b.count))[0];
    let worstMath = data.scoreOfTypes.slice(7, 10).sort( (a, b) => (a.score / a.count) - (b.score / b.count))[0];	
    
    // Clamp between 5 and 100
    let worstTopicBar = $derived(Math.max(5, Math.min( (worstTopic.score * 100 / worstTopic.count) + rand.intBetween(-8, 8), 100)));
    let worstReadingBar = $derived(Math.max(5, Math.min( (worstReading.score * 100 / worstReading.count) + rand.intBetween(-8, 8), 100)));
    let worstWritingBar = $derived(Math.max(5, Math.min( (worstWriting.score * 100 / worstWriting.count) + rand.intBetween(-8, 8), 100)));
    let worstMathBar = $derived(Math.max(5, Math.min( (worstMath.score * 100 / worstMath.count) + rand.intBetween(-8, 8), 100)));
</script>

<div class="container">
	<h1 class="analysis-title">Your Performance Analysis is Here!</h1>
	<div class="analysis-section">
		<div class="predicted-score-container">
			<div class="estimated-score">
					<H5>Your Estimated Score</H5>
					<p class="score" style="--score={predictedTotalScore}">{predictedTotalScore}</p>
			</div>
			<div class="score-per-section">
				<div class="section">
					<span class="section-score section-score--verbal">{predictedVerbalScore}</span>
					<div class="section-right">
						<P2 isBold={true}>{innerWidth > 540 ? "Reading & Writing" : "Verbal"}</P2>
					</div>
				</div>
				<div class="section">
					<span class="section-score section-score--math">{predictedMathScore}</span>
					<div class="section-right">
						<P2 isBold={true}>Math</P2>
					</div>
				</div>
			</div>
		</div>

        <SkillSummary {skills}/>

		<!-- Targetable Weaknesses -->
		<div class="weakness-analysis">
            <H5>Targetable Weaknesses</H5>
            <div class="weakness-container">
                <div class="weakness-left">
					<div class="weakness-title">
						<P3 isBold={true}>Category</P3>
					</div>
                    <div class="weaknesses">
                        {#if worstTopic.score != worstTopic.count}
                            <div class="weakness">Topic: {worstTopic.topic}</div>
                        {/if}
                        {#if worstReading.score != worstReading.count}
                            <div class="weakness">{worstReading.questionType} ({worstReading.CBType})</div>
                        {/if}
                        {#if worstWriting.score != worstWriting.count}
                            <div class="weakness">{worstWriting.questionType} ({worstWriting.CBType})</div>
                        {/if}
                        {#if worstMath.score != worstMath.count}
                            <div class="weakness">{worstMath.questionType} ({worstMath.CBType})</div>
                        {/if}
                    </div>
                </div>
                <div class="weakness-right">
					<div class="weakness-title">
						<P3 isBold={true}>Current Performance</P3>
					</div>
                    <div class="weakness-details">
                        {#if worstTopic.score != worstTopic.count}
                            <div class="weakness-bar-container">
                                <div class="weakness-bar" style="--width: {worstTopicBar}%"></div>
                            </div>
                        {/if}
                        {#if worstReading.score != worstReading.count}
                            <div class="weakness-bar-container">
                                <div class="weakness-bar" style="--width: {worstReadingBar}%"></div>
                            </div>
                        {/if}
                        {#if worstWriting.score != worstWriting.count}
                            <div class="weakness-bar-container">
                                <div class="weakness-bar" style="--width: {worstWritingBar}%"></div>
                            </div>
                        {/if}
                        {#if worstMath.score != worstMath.count}
                            <div class="weakness-bar-container">
                                <div class="weakness-bar" style="--width: {worstMathBar}%"></div>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
		<div class="cta">
			<H3>Take full-length simulations with thorough analyses</H3>
			<P2Wrapper>
				<p>You've just completed the mini-test, congratulations on taking the first step towards mastering the Digital SAT.</p>
				<p>However, this minitest does not provide enough data to give you a clear idea of your strengths and weaknesses, as well as to provide a study plan to increase your score most effectively.</p>
				<p>Get started when you’re ready!</p>
			</P2Wrapper>
			<div class="button-wrapper">
				<a href="#pricing"><Button>I'm Ready!</Button></a>
			</div>
		</div>
		<div id="pricing">
			<SprintCard 
				--box-shadow-color="var(--pitch-black)" 
				--max-width="none"
			/>
			<CommitmentCard 
				--box-shadow-color="var(--pitch-black)"
				--max-width="none"
			/>
		</div>
    </div>
</div>

<style>
	.container {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 4rem;
		padding-top: 3.4375rem;
		align-items: center;
		justify-content: center;
		background-color: #F5FDFF;
	}

	.analysis-title {
		font-size: 2rem;
		font-family: 'Inter';
		font-weight: 450;
	}

	.analysis-section {
		width: 50rem;
		display: flex;
		flex-direction: column;
		gap: 2.5rem;
		border-radius: 1rem;
	}

	.predicted-score-container {
        display: inline-flex;
        padding: 1.5rem;
		gap: 1.75rem;
		justify-content: flex-start;
        
        background-color: white;
        border: 1px solid var(--pitch-black);
        border-radius: 0.5rem;
        flex: 1 0 auto;
        background-color: var(--light-yellow);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .estimated-score {
        display: flex;
        flex-direction: column;
        width: fit-content;
    }

    .score {
        width: fit-content;
        font-size: 5.625rem;
        font-family: "Inter";
        font-weight: 800;
        -webkit-text-stroke: var(--pitch-black) 2px;
        paint-order: stroke fill;

        background: var(--sky-blue);
        background: linear-gradient(90deg, var(--sky-blue) 0%, var(--rose) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        -webkit-filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
        filter: drop-shadow(0.25rem 0.25rem var(--pitch-black));
    }

    .score-per-section {
        display: flex;
        flex-direction: column;
        gap: 0.0625rem;
		align-self: center;
    }

    .section {
        display: inline-flex;
        gap: 0.625rem
    }

    .section-score {
        font-size: 2.8125rem;
        font-family: "Inter";
        font-weight: 800;
        font-variant-numeric: tabular-nums;
    }

    .section-score--verbal {
        color: var(--sky-blue);
    }

    .section-score--math {
        color: var(--rose);
    }

    .section-right {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

	.weakness-analysis {
        display: flex;
        flex-direction: column;
        gap: 16px;
		border: 1px solid var(--pitch-black);
		border-radius: 0.5rem;
		padding: 2.5rem 1.25rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		background-color: var(--light-aquamarine);
    }

    .weakness-container {
        padding: 20px 0;
        border-radius: 12px;
        display: inline-flex;
        gap: 8px;
    }

    .weaknesses, .weakness-left,.weakness-details, .weakness-right {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

	.weakness-title {
		border-bottom: 1px solid var(--pitch-black);
		text-align: center;
	}

    .weakness-right {
        flex-grow: 1;
    }

    .weakness {
		font-family: "Inter";
        font-size: 16px;
        font-weight: 500;
    }

    .weakness-bar-container {
        height: 19.2px;
        display: flex;
        align-items: center;
    }

    .weakness-bar {
        background-color: var(--light-rose);
        width: 100%;
        height: 8px;
        border-radius: 10px;
		border: 1px solid var(--pitch-black);
        position: relative;
        display: flex;
    }

    .weakness-bar::before {
        content: "";
        background-color: var(--rose);
        border-radius: 10px;
        width: var(--width);
    }

	.cta {
		display: flex;
		flex-direction: column;
		gap: 1.25rem;
		border: 1px solid var(--pitch-black);
		border-radius: 0.75rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		padding: 2.1875rem;
        background-color: white;
	}

	.button-wrapper {
		display: flex;
		justify-content: center;
	}

	#pricing {
		margin: 8.4375rem 0;
		display: inline-flex;
		gap: 2.5rem;
		justify-content: space-between;
	}
</style>