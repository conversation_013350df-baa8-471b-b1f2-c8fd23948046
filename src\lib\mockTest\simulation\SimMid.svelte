<!-- 
    @component
    ## SimMid
    A component to display the middle section of the simulation test.
    
    ## Props
    - `data`: The data of the test.
    - `i`: The current question index.
    - `studentAnswers`: The student's answers.
    - `studentCross`: The student's crossed answers.
    - `isMarked`: The student's marked questions.
    - `setAnswer`: A function to set the student's answers.
    - `setMarked`: A function to set the student's marked questions.
    - `setCross`: A function to set the student's crossed answers.
    - `isMath`: A boolean to check if the current question is a math question.
    - `isCalculatorOpen`: A boolean to check if the calculator is open.
    - `isSPR`: A boolean to check if the current question is a SPR question.
    - `setContent`: A function to set the content of the current question.
    - `curPassage`: The current passage.
    - `curIntro`: The current introduction.
    - `curPassage_2`: The second passage.
-->

<script>
    import SPR from "$lib/mockTest/ui/MidSPR.svelte";
	import MidVerbal from '$lib/mockTest/ui/MidVerbal.svelte';
	import MidMath from '$lib/mockTest/ui/MidMath.svelte';
	import TwoSide from "../ui/TwoSide.svelte";

    /** @type {{i: any, data: any, studentAnswers: any, studentCross: any, isMarked: any, setAnswer: any, setMarked: any, setCross: any, isMath: any, isCalculatorOpen: any, isSPR: any, setContent: any, curPassage: any, curIntro: any, curPassage_2: any}} */
    let {
        i,
        data = $bindable(),
        studentAnswers,
        studentCross,
        isMarked,
        setAnswer,
        setMarked,
        setCross,
        isMath,
        isCalculatorOpen,
        isSPR,
        setContent = $bindable(),
        curPassage = $bindable(),
        curIntro = $bindable(),
        curPassage_2 = $bindable()
    } = $props();

    // Annotate
    setContent = () => {
        let intro = document.querySelector('.intro')?.innerHTML;
        let passage = document.querySelector('.passage');
        let passage2 = document.querySelector('.passage2')?.innerHTML;

        if (data.questions[i].questionType === "Student's Notes") {
            let tmpAr = passage.childNodes;
            for (let j = 0; j < tmpAr.length; j++) {
                data.questions[i].passage[j] = tmpAr[j].innerHTML;
            } 
        }
        else {
            data.questions[i].passage = passage.innerHTML;
        }

        if (data.questions[i].intro) data.questions[i].intro = intro;
        if (data.questions[i].passage2) data.questions[i].passage2 = passage2;
    }
    
    $effect(() => {
        curPassage = data.questions[i]?.passage;
        curIntro = data.questions[i]?.intro;
        curPassage_2 = data.questions[i]?.passage2;
    });


    let isEliminateTool = $state(false);

    function toggleElimination() {
        isEliminateTool = !isEliminateTool;
    }
</script>

<!-- Middle -->
 <TwoSide {isSPR} {isMath}>
    {#if isSPR}
    
        <SPR
            {data} 
            {i} 
            {isMarked} 
            {setMarked} 
            {isEliminateTool} 
            {toggleElimination} 
            {isCalculatorOpen} 
            {studentAnswers}
            {studentCross}
            {setAnswer}
            {setCross}
        />

    {:else if isMath}
        
        <MidMath 
            {data} 
            {i} 
            {isMarked} 
            {setMarked} 
            {isEliminateTool} 
            {toggleElimination} 
            {isSPR}
            {isCalculatorOpen} 
            {studentAnswers}
            {studentCross}
            {setAnswer}
            {setCross}
        />

    {:else}

        <MidVerbal 
            {data} 
            {i} 
            {studentAnswers} 
            {studentCross} 
            {isMarked} 
            {setAnswer} 
            {setMarked} 
            {setCross} 
            {curPassage} 
            {curIntro} 
            {curPassage_2} 
        />

    {/if}
 </TwoSide>
