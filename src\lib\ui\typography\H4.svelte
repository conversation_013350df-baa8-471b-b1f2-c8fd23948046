<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<!-- 
    @component
    A styled h4 element.
    
    Usage:
    ```tsx
    <H4>Heading 4</H4>
    ```
-->

<h4>
    {@render children?.()}
</h4>

<style>
    h4 {
        font-family: "Inter";
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-color, --pitch-black);
    }

    @media (max-width: 540px) {
        h4 {
            font-size: 1.25rem;
        }
    }
</style>
