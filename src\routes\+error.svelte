<script lang="ts">
	import { page } from '$app/state';
</script>

<section>
    <img src="https://api.memegen.link/images/grumpycat/{page.status}/{page.error?.message}.png?width=480" alt="error message">
</section>


<style>
    section {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 4rem;
        min-height: calc(100svh - 12rem);
    }

    img {
        width: 100%;
        max-width: 480px;
    }

    @media (max-width: 768px) {
        section {
            padding: 2rem 1rem;
        }
    }
</style>