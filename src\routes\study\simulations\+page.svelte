<script lang="ts">
	import { P2<PERSON><PERSON><PERSON>, Button, H4, P3 } from '$lib/ui';

	let { data } = $props();
	
	const simulationDetails = data.simulationDetails;
</script>

<svelte:head>
	<title>Simulations - DSAT16</title>
</svelte:head>

<div class="simulations-wrapper">
	<div class="simulations">
		<div class="title">
			Simulations
		</div>

		<P2Wrapper>
			<p>Full-length practice tests designed to closely resemble the style of the College Board’s Digital SAT. These simulations are (probably) 16% more challenging than the six practice tests offered by Bluebook, making them ideal for students aiming to score 1400 and above. </p>
			<p>With tougher questions and a format that mirrors the actual test, these practice tests will help you sharpen your skills and boost your confidence for test day.</p>
		</P2Wrapper>

		<div class="simulations-cards">
			{#each simulationDetails as simulation}
				<div class="simulations-card">
					<div class="first-row">
						<H4>Simulation 1</H4>
						<div class="arena-buttons">
							{#if simulation.progress.id}
							<a href="/study/analysis/{simulation.progress.id}">
								<Button
									isSecondary
								>
									Analysis
								</Button>
							</a>
							{/if}
							<a href="./simulations/{simulation.slug}">
								<Button
									--button-bg-color="var(--sky-blue)"
								>
									{simulation.progress.id ? "Retry" : "Start" }
								</Button>
							</a>
						</div>
                        <div class="not-support">
                            <P3>Mobile coming soon</P3>
                        </div>
					</div>
					<div class="progress-bar" style={` background: linear-gradient(90deg, #66E2FF ${(simulation.progress.score ?? 0) * 100 / 1600}%, #FFF 0%);`}>
						<div class="score-wrapper">
							<div class="score">{simulation.progress.score ?? '?'}/1600</div>
						</div>
					</div>
				</div>
			{/each}
			<div class="simulations-card coming-soon">
				<H4>Coming Soon</H4>
				<div class="coming-question">More Simulations will be added soon!</div>
			</div>
		</div>
	</div>
</div>


<style>
    .simulations-wrapper {
        padding: 2rem 2rem 1rem 2rem;
        background-color: var(--light-purple);
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .simulations {
        max-width: 75rem;
        display: flex;
        flex-direction: column;
        gap: 1.5625rem;
    }

    .title {
        color: var(--pitch-black);
        font-family: "Inter";
        font-size: 2.25rem;
        font-weight: 600;
    }

    .simulations-cards {
        display: grid;
        grid-gap: 1.25rem;
        width: 100%;
        grid-template-columns: repeat(auto-fit, minmax(26.25rem, 1fr));
    }

    .simulations-card {
        width: 100%;
        flex: 1 0 25.3125rem;
        /* max-width: 32.5rem; */
        height: 8.75rem;
        display: flex;
        padding: 1.25rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
        border-radius: 0.5rem;
        border: 0.0625rem solid #000;
        background: var(--light-sky-blue, #DAF8FF);
        box-shadow: 0.25rem 0.25rem 0rem 0rem #000;
    }

    .first-row {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .arena-buttons {
        display: inline-flex;
        align-items: flex-start;
        gap: 0.9375rem;
    }

    .progress-bar {
        position: relative;
        width: 100%;
        height: 1.5rem;
        flex-shrink: 0;
        border-radius: 1.875rem;
        border: 0.125rem solid #000;
        background: #FFF;
    }

    .score-wrapper {
        position: absolute;
        top: 50%;
        transform: translate(0, -50%);
        left: 0.5rem;
        width: 3.0625rem;
        height: 1.25rem;
        display: flex;
    }

    .score {
        color: #000;
        font-family: "Open Sans";
        font-size: 1.125rem;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        align-self: center;
    }

    .coming-soon {
        background-color: #F6F6F6;
        position: relative;
    }

    .coming-question {
        position: absolute;
        bottom: 1.375rem;
        color: #000;
        font-family: "Open Sans";
        font-size: 1.125rem;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .not-support {
        display: none;
    }

    @media (max-width: 60rem) {
        .arena-buttons {
            display: none;
        }

        .not-support {
            display: flex;
            color: #000;
            font-family: "Open Sans";
            font-size: 0.875rem;
            font-style: normal;
            font-weight: 450;
            line-height: 1.3125rem; /* 150% */
        }

        .simulations-cards {
            grid-template-columns: repeat(auto-fit, minmax(18.125rem, 1fr));
        }
    }
</style>
