<script>
	import { H5 } from '$lib/ui';

	/** @type {{sections: any, comingSoon: any}} */
	let { sections, comingSoon } = $props();
</script>

<nav>
	{#each sections as section, i}
		{#if i === 0}
			<a class="lecture-link" href={section.path}>
				{#if comingSoon}🔒{/if} <strong>{section.prefix}</strong>
				{section.title}
			</a>
		{:else}
			<div class="lecture-section">
				<H5 --text-color="white">{section.section}</H5>
				{#each section.lectures as lecture}
					<a class="lecture-link" href={lecture.path}>
						{#if comingSoon}🔒{/if} <strong>{lecture.prefix}</strong>
						{lecture.title}
					</a>
				{/each}
			</div>
		{/if}
	{/each}
	<div>
		<!-- <a href="/study/simulations"><H5 --text-color=white>Simulations</H5></a>
        <a href="/question-bank"><H5 --text-color=white>Question Bank</H5></a>
        <a href="/study/vocab-tool"><H5 --text-color=white>Vocab16 <span class="gradient">new</span></H5></a> -->
		<a href="/study" class="dashboard-link">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="24"
				height="24"
				viewBox="0 0 40 40"
				fill="none"
			>
				<path
					d="M13.8168 18.8163C13.6651 18.9748 13.5462 19.1618 13.4668 19.3663C13.3001 19.7721 13.3001 20.2272 13.4668 20.633C13.5462 20.8376 13.6651 21.0245 13.8168 21.183L18.8168 26.183C19.1307 26.4968 19.5563 26.6732 20.0002 26.6732C20.444 26.6732 20.8697 26.4968 21.1835 26.183C21.4973 25.8692 21.6736 25.4435 21.6736 24.9997C21.6736 24.5558 21.4973 24.1302 21.1835 23.8163L19.0168 21.6663H25.0002C25.4422 21.6663 25.8661 21.4907 26.1787 21.1782C26.4912 20.8656 26.6668 20.4417 26.6668 19.9997C26.6668 19.5576 26.4912 19.1337 26.1787 18.8212C25.8661 18.5086 25.4422 18.333 25.0002 18.333H19.0168L21.1835 16.183C21.3397 16.0281 21.4637 15.8437 21.5483 15.6406C21.6329 15.4375 21.6765 15.2197 21.6765 14.9997C21.6765 14.7797 21.6329 14.5618 21.5483 14.3587C21.4637 14.1556 21.3397 13.9713 21.1835 13.8163C21.0286 13.6601 20.8442 13.5361 20.6411 13.4515C20.438 13.3669 20.2202 13.3233 20.0002 13.3233C19.7801 13.3233 19.5623 13.3669 19.3592 13.4515C19.1561 13.5361 18.9718 13.6601 18.8168 13.8163L13.8168 18.8163ZM3.3335 19.9997C3.3335 23.296 4.31098 26.5184 6.14234 29.2592C7.97369 32 10.5767 34.1362 13.6221 35.3977C16.6675 36.6591 20.0187 36.9892 23.2517 36.3461C26.4847 35.703 29.4544 34.1157 31.7853 31.7848C34.1162 29.4539 35.7035 26.4842 36.3466 23.2512C36.9897 20.0182 36.6596 16.6671 35.3982 13.6216C34.1367 10.5762 32.0005 7.9732 29.2597 6.14185C26.5188 4.31049 23.2965 3.33301 20.0002 3.33301C17.8115 3.33301 15.6442 3.7641 13.6221 4.60168C11.6 5.43926 9.76269 6.66692 8.21505 8.21456C5.08944 11.3402 3.3335 15.5794 3.3335 19.9997ZM33.3335 19.9997C33.3335 22.6368 32.5515 25.2146 31.0864 27.4073C29.6213 29.5999 27.539 31.3089 25.1026 32.3181C22.6663 33.3272 19.9854 33.5913 17.399 33.0768C14.8125 32.5623 12.4368 31.2925 10.5721 29.4278C8.70737 27.5631 7.4375 25.1873 6.92303 22.6009C6.40856 20.0145 6.6726 17.3336 7.68177 14.8972C8.69094 12.4609 10.3999 10.3785 12.5926 8.91341C14.7852 7.44833 17.3631 6.66634 20.0002 6.66634C23.5364 6.66634 26.9278 8.0711 29.4283 10.5716C31.9287 13.0721 33.3335 16.4635 33.3335 19.9997Z"
					fill="white"
				/>
			</svg>
			<H5 --text-color="var(--sky-blue)">Back to Dashboard</H5>
		</a>
	</div>
</nav>

<style>
	nav {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.lecture-link {
		display: block;
		font-size: 1rem;
		color: rgba(255, 255, 255, 0.7);
		font-family: 'Open Sans';
		font-weight: 400;
		max-width: 16rem;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}

	strong {
		font-size: 1.125rem;
		color: white;
	}

	.dashboard-link {
		display: flex;
		gap: 0.5rem;
        align-items: center;
	}

	@media (max-width: 1024px) {
		nav {
			height: 100svh;
			justify-content: center;
			margin-left: 1.25rem;
		}
	}
</style>
