<script>
	import { dev } from '$app/environment';
	import { onNavigate } from '$app/navigation';
	import { NavContent } from '$lib/bootcamp';
    import { onMount } from 'svelte';
    import { slide } from 'svelte/transition';

    let isNavDropdownOpen = $state(false);
    /** @type {{data: any, children?: import('svelte').Snippet}} */
    let { children } = $props();

    const lecturesPath = "/bootcamp/lectures";

    const sections = [
        {
            title: "Overview",
            path: `${lecturesPath}/0`,
            prefix: "Lecture 0"
        },
        {
            section: "Writing",
            lectures: [
                { title: "Transitions & Student's Notes", path: `${lecturesPath}/1`, prefix: "Lecture W1" },
                { title: "Punctuation", path: `${lecturesPath}/2`, prefix: "Lecture W2" },
                { title: "Grammar", path: `${lecturesPath}/3`, prefix: "Lecture W3" }
            ]
        },
        {
            section: "Reading",
            lectures: [
                { title: "Words in Context", path: `${lecturesPath}/4`, prefix: "Lecture R1" },
                { title: "Reading Comprehension", path: `${lecturesPath}/5`, prefix: "Lecture R2" },
                { title: "Critical Reading", path: `${lecturesPath}/6`, prefix: "Lecture R3" },
                { title: "Synthesis Reading", path: `${lecturesPath}/7`, prefix: "Lecture R4" }
            ]
        },
        {
            section: "Math",
            lectures: [
                { title: "Algebra", path: `${lecturesPath}/8`, prefix: "Lecture M1" },
                { title: "Data Analysis", path: `${lecturesPath}/9`, prefix: "Lecture M2" },
                { title: "Geometry & Trigonometry", path: `${lecturesPath}/10`, prefix: "Lecture M3" }
            ]
        }
    ];

    function changeIcon() {
        isNavDropdownOpen = !isNavDropdownOpen;
    }

    // Clipboard prevention code...
    onMount(() => {
        ["copy", "cut"].forEach(event => 
            document.addEventListener(event, async () => {
                if (dev) return;
                
                if (!navigator.clipboard) {
                    const aux = document.createElement("input");
                    aux.setAttribute("value", "");
                    document.body.appendChild(aux);
                    aux.select();
                    document.execCommand("copy");
                    document.body.removeChild(aux);
                } else {
                    await navigator.clipboard.writeText("").catch();
                }
            })
        );
    });

    onNavigate(() => {
        isNavDropdownOpen = false;
    });
</script>

<svelte:head>
    <meta name="robots" content="noindex">
</svelte:head>

<div class="note-container">
    <!-- PC -->
    <div class="nav">
        <a href="/" aria-label="To homepage">
            <svg xmlns="http://www.w3.org/2000/svg" width="158" height="32" viewBox="0 0 158 32" fill="none">
                <path d="M126.509 0.425856V31.5741H118.996V7.45247H118.813L111.848 11.711V5.20152L119.528 0.425856H126.509Z" fill="url(#paint0_linear_1243_715)"/>
                <path d="M145.882 32C144.178 32 142.546 31.7262 140.985 31.1787C139.423 30.621 138.034 29.7389 136.817 28.5323C135.601 27.3156 134.642 25.7237 133.943 23.7567C133.243 21.7795 132.898 19.3714 132.908 16.5323C132.919 13.967 133.233 11.6654 133.851 9.62738C134.47 7.57922 135.352 5.8403 136.498 4.41065C137.654 2.98099 139.033 1.891 140.635 1.14068C142.247 0.380228 144.047 0 146.034 0C148.214 0 150.135 0.425856 151.798 1.27757C153.471 2.11914 154.81 3.25475 155.813 4.68441C156.817 6.10393 157.41 7.68568 157.593 9.42966H150.186C149.963 8.44613 149.471 7.70089 148.711 7.19392C147.96 6.67681 147.068 6.41825 146.034 6.41825C144.128 6.41825 142.703 7.24461 141.76 8.89734C140.827 10.5501 140.351 12.7605 140.331 15.5285H140.528C140.954 14.5957 141.568 13.7947 142.369 13.1255C143.17 12.4563 144.087 11.9442 145.121 11.5894C146.166 11.2243 147.271 11.0418 148.437 11.0418C150.303 11.0418 151.95 11.4727 153.38 12.3346C154.81 13.1965 155.93 14.3777 156.741 15.8783C157.552 17.3688 157.953 19.0773 157.943 21.0038C157.953 23.1736 157.446 25.09 156.422 26.7529C155.398 28.4056 153.978 29.6933 152.163 30.616C150.358 31.5387 148.265 32 145.882 32ZM145.836 26.2205C146.759 26.2205 147.585 26.0025 148.315 25.5665C149.045 25.1305 149.618 24.5374 150.034 23.7871C150.45 23.0368 150.652 22.1901 150.642 21.2471C150.652 20.294 150.45 19.4474 150.034 18.7072C149.628 17.967 149.061 17.379 148.331 16.943C147.611 16.507 146.784 16.289 145.851 16.289C145.172 16.289 144.538 16.4157 143.95 16.6692C143.362 16.9227 142.85 17.2776 142.414 17.7338C141.988 18.18 141.654 18.7072 141.41 19.3156C141.167 19.9138 141.04 20.5627 141.03 21.2624C141.04 22.185 141.253 23.0215 141.669 23.7719C142.085 24.5222 142.652 25.1204 143.372 25.5665C144.092 26.0025 144.914 26.2205 145.836 26.2205Z" fill="url(#paint1_linear_1243_715)"/>
                <path d="M11.0336 30.8601H0V1.00967H11.019C14.0604 1.00967 16.6791 1.60727 18.8752 2.80245C21.0809 3.98792 22.7814 5.6981 23.9766 7.93299C25.1717 10.1582 25.7693 12.8206 25.7693 15.9203C25.7693 19.0297 25.1717 21.7019 23.9766 23.9368C22.7911 26.1717 21.0955 27.8867 18.8897 29.0819C16.684 30.2674 14.0653 30.8601 11.0336 30.8601ZM7.21483 24.7093H10.7567C12.428 24.7093 13.8418 24.4275 14.9981 23.8639C16.1641 23.2906 17.0435 22.3626 17.6362 21.08C18.2387 19.7877 18.5399 18.0678 18.5399 15.9203C18.5399 13.7729 18.2387 12.0627 17.6362 10.7898C17.0338 9.50714 16.1447 8.58403 14.969 8.02045C13.8029 7.44715 12.3648 7.1605 10.6546 7.1605H7.21483V24.7093Z" fill="white"/>
                <path d="M46.1166 9.95897C46.0194 8.89011 45.587 8.05931 44.8194 7.46658C44.0615 6.86413 42.978 6.56291 41.5691 6.56291C40.6363 6.56291 39.8589 6.68437 39.237 6.92729C38.6151 7.17022 38.1487 7.50545 37.8378 7.93299C37.5268 8.35082 37.3665 8.83181 37.3568 9.37596C37.3373 9.82294 37.4248 10.2165 37.6191 10.5566C37.8232 10.8967 38.1147 11.1979 38.4937 11.4602C38.8823 11.7129 39.3488 11.9364 39.8929 12.1307C40.4371 12.3251 41.0492 12.4951 41.7294 12.6409L44.2947 13.2239C45.7717 13.5445 47.0737 13.9721 48.2009 14.5065C49.3378 15.0409 50.29 15.6774 51.0577 16.4159C51.835 17.1544 52.4229 18.0046 52.8213 18.9666C53.2197 19.9286 53.4238 21.0071 53.4335 22.2023C53.4238 24.0874 52.9476 25.7053 52.0051 27.0559C51.0625 28.4066 49.707 29.4414 47.9385 30.1605C46.1798 30.8795 44.0566 31.2391 41.5691 31.2391C39.0718 31.2391 36.8952 30.865 35.0393 30.1168C33.1834 29.3686 31.7404 28.2317 30.7104 26.7061C29.6804 25.1806 29.1508 23.2517 29.1217 20.9197H36.0304C36.0887 21.8817 36.3462 22.6833 36.8029 23.3246C37.2596 23.9659 37.8864 24.4518 38.6831 24.7822C39.4897 25.1125 40.4225 25.2777 41.4816 25.2777C42.4533 25.2777 43.2793 25.1466 43.9594 24.8842C44.6493 24.6218 45.1789 24.2575 45.5482 23.791C45.9174 23.3246 46.1069 22.7902 46.1166 22.1877C46.1069 21.6242 45.932 21.1432 45.5919 20.7448C45.2518 20.3367 44.7271 19.9869 44.0177 19.6954C43.3181 19.3941 42.4242 19.1172 41.3359 18.8645L38.2167 18.1358C35.632 17.543 33.5963 16.5859 32.1096 15.2644C30.6229 13.9332 29.8845 12.1356 29.8942 9.87152C29.8845 8.02531 30.38 6.40743 31.3809 5.01791C32.3817 3.62839 33.7664 2.54495 35.5349 1.7676C37.3033 0.990241 39.3196 0.601562 41.5837 0.601562C43.8963 0.601562 45.9028 0.995099 47.6033 1.78217C49.3135 2.55953 50.6399 3.65268 51.5824 5.06164C52.5249 6.4706 53.0059 8.10304 53.0254 9.95897H46.1166Z" fill="white"/>
                <path d="M62.6123 30.8601H54.8582L64.9298 1.00967H74.535L84.6066 30.8601H76.8525L69.8417 8.53059H69.6085L62.6123 30.8601ZM61.5775 19.1123H77.7853V24.5927H61.5775V19.1123Z" fill="white"/>
                <path d="M83.1163 6.86899V1.00967H108.346V6.86899H99.295V30.8601H92.1822V6.86899H83.1163Z" fill="white"/>
                <defs>
                <linearGradient id="paint0_linear_1243_715" x1="160.134" y1="-6.25806" x2="124.623" y2="-11.4802" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#66E2FF"/>
                    <stop offset="1" stop-color="#FF66C4"/>
                </linearGradient>
                <linearGradient id="paint1_linear_1243_715" x1="160.134" y1="-6.25806" x2="124.623" y2="-11.4802" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#66E2FF"/>
                    <stop offset="1" stop-color="#FF66C4"/>
                </linearGradient>
                </defs>
            </svg>
        </a>
        <NavContent {sections} comingSoon={false}/>
    </div>
    
    <!-- Mobile -->
    <div class="nav-mobile">
        <a href="/" aria-label="To homepage">
            <svg xmlns="http://www.w3.org/2000/svg" width="119" height="24" viewBox="0 0 119 24" fill="none">
                <path d="M94.8819 0.319392V23.6806H89.247V5.58935H89.1101L83.8857 8.78327V3.90114L89.6462 0.319392H94.8819Z" fill="url(#paint0_linear_924_1940)"/>
                <path d="M109.411 24C108.134 24 106.91 23.7947 105.738 23.384C104.567 22.9658 103.525 22.3042 102.613 21.3992C101.7 20.4867 100.982 19.2928 100.457 17.8175C99.9323 16.3346 99.6738 14.5285 99.6814 12.3992C99.689 10.4753 99.9247 8.74905 100.389 7.22053C100.852 5.68441 101.514 4.38023 102.373 3.30798C103.24 2.23574 104.275 1.41825 105.476 0.855513C106.685 0.285171 108.035 0 109.525 0C111.16 0 112.602 0.319392 113.849 0.958175C115.103 1.58935 116.107 2.44106 116.86 3.51331C117.613 4.57795 118.058 5.76426 118.195 7.07224H112.64C112.472 6.3346 112.103 5.77566 111.533 5.39544C110.97 5.0076 110.301 4.81369 109.525 4.81369C108.096 4.81369 107.027 5.43346 106.32 6.673C105.621 7.91255 105.263 9.57034 105.248 11.6464H105.396C105.716 10.9468 106.176 10.346 106.776 9.84411C107.377 9.34221 108.065 8.95817 108.841 8.69201C109.624 8.41825 110.453 8.28137 111.328 8.28137C112.727 8.28137 113.963 8.60456 115.035 9.25095C116.107 9.89734 116.948 10.7833 117.556 11.9087C118.164 13.0266 118.465 14.308 118.457 15.7529C118.465 17.3802 118.084 18.8175 117.316 20.0646C116.548 21.3042 115.484 22.27 114.122 22.962C112.769 23.654 111.198 24 109.411 24ZM109.377 19.6654C110.069 19.6654 110.689 19.5019 111.237 19.1749C111.784 18.8479 112.214 18.403 112.525 17.8403C112.837 17.2776 112.989 16.6426 112.982 15.9354C112.989 15.2205 112.837 14.5856 112.525 14.0304C112.221 13.4753 111.795 13.0342 111.248 12.7072C110.708 12.3802 110.088 12.2167 109.389 12.2167C108.879 12.2167 108.404 12.3118 107.963 12.5019C107.522 12.692 107.138 12.9582 106.811 13.3004C106.491 13.635 106.24 14.0304 106.058 14.4867C105.875 14.9354 105.78 15.4221 105.773 15.9468C105.78 16.6388 105.94 17.2662 106.252 17.8289C106.563 18.3916 106.989 18.8403 107.529 19.1749C108.069 19.5019 108.685 19.6654 109.377 19.6654Z" fill="url(#paint1_linear_924_1940)"/>
                <path d="M8.27519 23.1451H0V0.757256H8.26426C10.5453 0.757256 12.5093 1.20545 14.1564 2.10184C15.8107 2.99094 17.086 4.27357 17.9824 5.94975C18.8788 7.61863 19.327 9.61546 19.327 11.9402C19.327 14.2723 18.8788 16.2764 17.9824 17.9526C17.0933 19.6288 15.8216 20.915 14.1673 21.8114C12.513 22.7005 10.549 23.1451 8.27519 23.1451ZM5.41112 18.532H8.06749C9.32098 18.532 10.3813 18.3206 11.2486 17.8979C12.1231 17.468 12.7826 16.772 13.2272 15.81C13.679 14.8407 13.9049 13.5508 13.9049 11.9402C13.9049 10.3297 13.679 9.04702 13.2272 8.09233C12.7753 7.13035 12.1085 6.43802 11.2267 6.01534C10.3522 5.58536 9.27361 5.37037 7.99097 5.37037H5.41112V18.532Z" fill="white"/>
                <path d="M34.5875 7.46923C34.5146 6.66758 34.1903 6.04449 33.6145 5.59994C33.0461 5.1481 32.2335 4.92218 31.1768 4.92218C30.4772 4.92218 29.8942 5.01328 29.4278 5.19547C28.9613 5.37766 28.6115 5.62909 28.3783 5.94975C28.1451 6.26312 28.0249 6.62386 28.0176 7.03197C28.003 7.3672 28.0686 7.66236 28.2144 7.91743C28.3674 8.1725 28.586 8.39842 28.8702 8.59518C29.1618 8.78466 29.5116 8.95228 29.9197 9.09804C30.3278 9.24379 30.7869 9.37132 31.2971 9.48064L33.221 9.9179C34.3287 10.1584 35.3053 10.4791 36.1507 10.8799C37.0033 11.2807 37.7175 11.758 38.2932 12.3119C38.8763 12.8658 39.3172 13.5035 39.616 14.2249C39.9148 14.9464 40.0678 15.7554 40.0751 16.6517C40.0678 18.0656 39.7107 19.279 39.0038 20.292C38.2969 21.3049 37.2803 22.0811 35.9539 22.6204C34.6348 23.1597 33.0425 23.4293 31.1768 23.4293C29.3039 23.4293 27.6714 23.1487 26.2795 22.5876C24.8875 22.0264 23.8053 21.1738 23.0328 20.0296C22.2603 18.8854 21.8631 17.4388 21.8413 15.6898H27.0228C27.0665 16.4112 27.2597 17.0125 27.6022 17.4935C27.9447 17.9745 28.4148 18.3388 29.0124 18.5866C29.6172 18.8344 30.3169 18.9583 31.1112 18.9583C31.84 18.9583 32.4594 18.8599 32.9696 18.6631C33.487 18.4664 33.8842 18.1931 34.1611 17.8433C34.4381 17.4935 34.5802 17.0926 34.5875 16.6408C34.5802 16.2181 34.449 15.8574 34.1939 15.5586C33.9388 15.2525 33.5453 14.9901 33.0133 14.7715C32.4886 14.5456 31.8181 14.3379 31.0019 14.1484L28.6625 13.6018C26.724 13.1573 25.1972 12.4394 24.0822 11.4483C22.9672 10.4499 22.4133 9.10168 22.4206 7.40364C22.4133 6.01898 22.785 4.80558 23.5356 3.76343C24.2863 2.72129 25.3248 1.90871 26.6511 1.3257C27.9775 0.742681 29.4897 0.451172 31.1877 0.451172C32.9222 0.451172 34.4271 0.746324 35.7025 1.33663C36.9851 1.91964 37.9799 2.73951 38.6868 3.79623C39.3937 4.85295 39.7544 6.07728 39.769 7.46923H34.5875Z" fill="white"/>
                <path d="M46.9592 23.1451H41.1437L48.6974 0.757256H55.9013L63.455 23.1451H57.6394L52.3813 6.39794H52.2064L46.9592 23.1451ZM46.1831 14.3343H58.339V18.4445H46.1831V14.3343Z" fill="white"/>
                <path d="M62.3372 5.15174V0.757256H81.2597V5.15174H74.4712V23.1451H69.1366V5.15174H62.3372Z" fill="white"/>
                <defs>
                  <linearGradient id="paint0_linear_924_1940" x1="120.1" y1="-4.69355" x2="93.4672" y2="-8.61015" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#66E2FF"/>
                    <stop offset="1" stop-color="#FF66C4"/>
                  </linearGradient>
                  <linearGradient id="paint1_linear_924_1940" x1="120.1" y1="-4.69355" x2="93.4672" y2="-8.61015" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#66E2FF"/>
                    <stop offset="1" stop-color="#FF66C4"/>
                  </linearGradient>
                </defs>
            </svg>
        </a>
        <button class="nav-icon" onclick={changeIcon}>
            {#if !isNavDropdownOpen}
            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="16.1" viewBox="0 0 23 17" fill="none">
                <path d="M0 0H23C23 1.27025 21.9703 2.3 20.7 2.3H0V0Z" fill="white"/>
                <path d="M0 6.8999H23C23 8.17016 21.9703 9.1999 20.7 9.1999H0V6.8999Z" fill="white"/>
                <path d="M0 13.7998H23C23 15.0701 21.9703 16.0998 20.7 16.0998H0V13.7998Z" fill="white"/>
            </svg>
            {:else}
            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="#FFF">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9.4093 11.4998L0 20.9091L2.09091 23L11.5002 13.5907L20.9095 23L23.0004 20.9091L13.5911 11.4998L23 2.09091L20.9091 0L11.5002 9.40887L2.09134 0L0.000431162 2.09091L9.4093 11.4998Z" fill="white"/>
            </svg>
            {/if}
        </button>
    </div>
    <hr>
    
    {#if isNavDropdownOpen}
    <div class="dropdown" transition:slide={{ duration: 120 }}>
        <NavContent {sections} comingSoon={false} />
    </div>
    {/if}
    
    {@render children?.()}
</div>

<style>
    a {
        text-decoration: none;
    }

    .note-container {
        display: inline-flex;
        justify-content: flex-start;
    }

    svg {
        flex-shrink: 0;
    }

    .nav {
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        width: 20rem;
        padding: 4rem 1.5rem 2rem 2rem;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        gap: 2.5rem;
        background-color: var(--pitch-black, #000);
        color: #FFF;
        overflow: auto;
    }

    .nav-mobile {
        display: none;
    }

    .dropdown {
        display: none;
    }

    hr {
        display: none;
    }

    @media (max-width: 1024px) {
        .note-container {
            display: flex;
            flex-direction: column;
        }

        .nav {
            display: none;        
       }


       .nav-mobile {
            height: 64px;
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            display: inline-flex;
            padding: 14px 20px;
            justify-content: space-between;
            align-items: center;
            background: var(--web-color-pitch-black, #000);
            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
            z-index: 2;
            border-bottom: 1px #FFF;
       }

       .dropdown {
            display: block;
            position: absolute;
            overflow-y: hidden;
            touch-action: none;
            width: 100vw;
            height: 100vh;
            background-color: #000;
            transition: all 0.1s;
            z-index: 1;
            position: fixed;
       }

       .nav-icon {
            background-color: var(--web-color-pitch-black, #000);
            border: none;
       }

       .nav-icon:hover {
            cursor: pointer;
       }

       hr {
            display: flex;
            width: 100%;
            height: 1px;
            margin-top: 64px;
            z-index: 2;
            position: fixed;
       }
    }

    /* width */
    ::-webkit-scrollbar {
        width: 6px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: var(--rose);
        border-radius: 5px;
    }
</style>