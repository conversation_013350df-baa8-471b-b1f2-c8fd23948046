<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<!-- 
    @component
    A wrapper component for P3 pargraphs on figma.
    
    Usage:
    ```tsx
    <P3Wrapper>
        <p>Lorem Ipsum</p>
    </P3Wrapper>
    ```
-->

<div class="wrapper">
    {@render children?.()}
</div>

<style>
    .wrapper {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        
        font-family: "Open Sans";
        font-size: 1rem;
        font-weight: 450;
        line-height: 1.5rem;
    }
</style>