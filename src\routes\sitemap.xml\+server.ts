const site = 'https://www.dsat16.com'; // change this to reflect your domain
const pages: { slug: string, lastMod: string }[] = [
    {
      slug: "simulation",
      lastMod: "2024-12-21"
    },
    {
      slug: "",
      lastMod: "2024-12-21"
    },
    {
      slug: "privacy-policy",
      lastMod: "2024-9-15"
    },
    {
      slug: "terms-and-conditions",
      lastMod: "2024-9-15"
    },
]; // populate this with all the slugs you wish to include

export async function GET({ url }): Promise<Response> {
    const body = sitemap(pages);
    const response = new Response(body);
    response.headers.set('Cache-Control', 'max-age=0, s-maxage=3600');
    response.headers.set('Content-Type', 'application/xml');
    return response;
}

const sitemap = (pages: { slug: string, lastMod: string }[]) => `<?xml version="1.0" encoding="UTF-8"?>
<urlset
  xmlns="https://www.sitemaps.org/schemas/sitemap/0.9"
  xmlns:news="https://www.google.com/schemas/sitemap-news/0.9"
  xmlns:xhtml="https://www.w3.org/1999/xhtml"
  xmlns:mobile="https://www.google.com/schemas/sitemap-mobile/1.0"
  xmlns:image="https://www.google.com/schemas/sitemap-image/1.1"
  xmlns:video="https://www.google.com/schemas/sitemap-video/1.1"
>
  ${pages
	.map(
		(page) => `
  <url>
    <loc>${site}/${page.slug}</loc>
    <changefreq>daily</changefreq>
    <lastmod>${page.lastMod}</lastmod>
    <priority>0.5</priority>
  </url>
  `
	)
	.join('')}
</urlset>`;