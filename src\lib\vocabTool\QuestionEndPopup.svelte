<script lang="ts">
	import { H4, P2, But<PERSON> } from "$lib/ui";

    // Component props
    let { 
        currentPhase = $bindable(0), 
        handleNextQuestion, 
        currentCard 
    } = $props();

    // State variables
    let correctAnswer = $state(currentCard.meaning);
    let nextButtonElement: HTMLButtonElement;

    /**
     * Focuses the next button element
     */
    export function focus() {
        nextButtonElement?.focus();
    }
</script>

{#if currentPhase === 3}
    <img 
        class="question-end-popup-image" 
        src="/image/incorrect.png" 
        alt="Incorrect answer feedback" 
    />
    <H4>
        You'll get it next time!
    </H4>
{:else if currentPhase === 1 || currentPhase === 2}
    <img 
        class="question-end-popup-image" 
        src="/image/halfcorrect.png" 
        alt="Partially correct answer feedback" 
    />
    <H4>
        Good job!
    </H4>
{:else}
    <img 
        class="question-end-popup-image" 
        src="/image/correct.png" 
        alt="Correct answer feedback" 
    />
    <H4>
        Good job!
    </H4>
{/if}

<P2>
    The correct answer is <b>{correctAnswer}</b>
</P2>
<Button onclick={handleNextQuestion}>
    Next
</Button>

<style>
    /* Main popup container with entrance animation */
    .question-end-popup {
        display: flex;
        flex-direction: column;
        gap: 2.25rem;
        justify-content: center;
        align-items: center;
        padding: 2rem 1rem;
        margin: 0 auto;
        background-color: white;
        border-radius: 0.75rem;
        border: 2px solid black;
        width: 100%;
        max-width: 25rem;
        font-family: 'Open Sans', sans-serif;
        animation: popupAppear 0.5s ease-out;
    }

    /* Popup entrance animation */
    @keyframes popupAppear {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Feedback image with bounce animation */
    .question-end-popup-image {
        object-fit: contain;
        height: 200px;
        animation: imageBounce 0.5s ease-out 0.2s both;
    }

    @keyframes imageBounce {
        0% {
            transform: scale(0.8);
            opacity: 0;
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Feedback text animations */
    .feedback-text {
        font-size: 1.2rem;
        animation: textSlideUp 0.5s ease-out 0.3s both;
        text-align: center;
    }

    .correct-answer-text {
        font-size: 1.2rem;
        animation: textSlideUp 0.5s ease-out 0.5s both;
        text-align: center;
    }

    @keyframes textSlideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Next button with hover and active states */
    .next-button {
        flex: 1;
        padding: 0.75rem 1.5rem;
        border-radius: 1rem;
        border: 2px solid black;
        white-space: nowrap;
        cursor: pointer;
        font: bold 1.5rem 'Open Sans', sans-serif;
        box-shadow: 5px 5px black;
        transition: all 0.1s ease;
        background-color: #67e8f9;
        outline: none;
    }

    .next-button:hover {
        transform: translateY(-2px) !important;
        box-shadow: 7px 7px black;
    }

    .next-button:active {
        transform: translate(5px, 5px) !important;
        box-shadow: 0 0 black;
    }
</style>