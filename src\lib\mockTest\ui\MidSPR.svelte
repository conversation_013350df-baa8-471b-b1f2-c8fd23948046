<script>
    import MidSprLeft from '$lib/mockTest/ui/MidSPRLeft.svelte';
	import MidMath from '$lib/mockTest/ui/MidMath.svelte';


    /** @type {{data: any, i: any, studentAnswers: any, isMarked: any, setMarked: any, studentCross: any, setAnswer: any, setCross: any, isCalculatorOpen: any, isEliminateTool: any, toggleElimination: any}} */
    let {
        data,
        i,
        studentAnswers,
        isMarked,
        setMarked,
        studentCross,
        setAnswer,
        setCross,
        isCalculatorOpen,
        isEliminateTool,
        toggleElimination
    } = $props();

    const isSPR = true;
</script>

<MidSprLeft />

<MidMath 
    {data} 
    {i} 
    {isMarked} 
    {setMarked} 
    {isEliminateTool} 
    {toggleElimination} 
    {isSPR}
    {isCalculatorOpen} 
    {studentAnswers}
    {studentCross}
    {setAnswer}
    {setCross}
/>