<script lang="ts">
	import { P2 } from "$lib/ui";

    interface Props {
        title: string;
        image: string;
    }

    let { title, image }: Props = $props();
</script>

<div class="window-container">
    <div class="header">
        <span class="title"><P2 --text-color=white>{title}</P2></span>
        <div class="buttons">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="9" cy="9" r="8" fill="var(--tangerine)" stroke="black"/>
            </svg>
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="9" cy="9" r="8" fill="var(--aquamarine)" stroke="black"/>
            </svg>
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="9" cy="9" r="8" fill="var(--rose)" stroke="black"/>
            </svg>            
        </div>
    </div>
    <!-- TODO: Add lazy loading -->
     <div class="image-container">
        <enhanced:img
            src={image}
            class="content-image"
            alt={title}
            sizes="min(720px, 100vw)"
            loading="lazy"
        />
     </div>

</div>

<style>
    .window-container {
        box-shadow: 1.25rem 1.25rem 0 #000;
        display: flex;
        width: 100%;
        max-width: 45rem;
        flex-direction: column;
        border-radius: 0.75rem 0.75rem 0 0;
    }

    .header {
        border: 0.125rem solid #000;
        border-bottom: none;
        border-radius: 0.75rem 0.75rem 0 0;
        background: var(--purple, #8c53ff);
        width: 100%;
        padding: 0.125rem 1.25rem;

        display: flex;
        align-items: center;
    }

    .buttons {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
    }

    .title {
        flex-grow: 1;
        text-align: center;
    }

    .image-container {
        border: 0.125rem solid #000;
        width: 100%;
        max-height: fit-content;
        aspect-ratio: 720 / 402;
        background-color: var(--light-sky-blue);
    }

    .content-image {
        object-fit: contain;
        width: 100%;
        height: 100%;
    }

    @media (max-width: 768px) {
        .window-container {
            box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        }
    }
</style>