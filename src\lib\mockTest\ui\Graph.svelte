<script>
    /** @type {{graph: any, isMath: any}} */
    let { graph, isMath } = $props();
</script>

<div class={isMath ? "graph-math" : "graph"}>
    <img src={graph} alt={graph}>
</div>

<style>
    .graph {
        margin-bottom: 16px;
        width: auto;
    }

    .graph img {
        object-fit: contain;
        width: 90%;
    }

    .graph-math {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .graph-math img {
        object-fit: contain;
        width: 380px;
    }    
</style>